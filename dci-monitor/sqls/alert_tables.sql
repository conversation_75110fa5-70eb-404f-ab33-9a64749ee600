-- 告警记录表
CREATE TABLE IF NOT EXISTS `monitor_alert` (
  `id` varchar(36) NOT NULL COMMENT '告警ID',
  `name` varchar(255) NOT NULL COMMENT '告警名称',
  `source` varchar(50) NOT NULL COMMENT '告警来源(prometheus/elasticsearch)',
  `level` varchar(20) NOT NULL COMMENT '告警级别(critical/warning/info)',
  `status` varchar(20) NOT NULL COMMENT '告警状态(firing/resolved/acknowledged/suppressed)',
  `description` text NOT NULL COMMENT '告警描述',
  `labels` json DEFAULT NULL COMMENT '告警标签',
  `annotations` json DEFAULT NULL COMMENT '告警注释',
  `starts_at` timestamp NOT NULL COMMENT '告警首次触发时间',
  `ends_at` timestamp NULL DEFAULT NULL COMMENT '告警结束时间（仅当告警已解决时有效）',
  `notification_due_at` timestamp NULL DEFAULT NULL COMMENT 'AlertManager下次通知时间（未解决告警的静默截止时间）',
  `resolved_at` timestamp NULL DEFAULT NULL COMMENT '告警实际解决时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `generator_url` varchar(255) DEFAULT NULL COMMENT '告警生成URL',
  `rule_id` varchar(36) DEFAULT NULL COMMENT '关联的告警规则ID',
  
  -- 确认相关字段
  `acknowledged_by` varchar(100) DEFAULT NULL COMMENT '确认人',
  `acknowledged_at` timestamp NULL DEFAULT NULL COMMENT '确认时间',
  `acknowledgement_type` varchar(20) DEFAULT NULL COMMENT '确认方式(manual/auto/api)',
  `note` text DEFAULT NULL COMMENT '处理备注',
  PRIMARY KEY (`id`),
  KEY `idx_source_status` (`source`, `status`),
  KEY `idx_starts_at` (`starts_at`),
  KEY `idx_level` (`level`),
  KEY `idx_rule_id` (`rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警记录表';

-- 告警规则表
CREATE TABLE IF NOT EXISTS `monitor_alert_rule` (
  `id` varchar(36) NOT NULL COMMENT '规则ID',
  `name` varchar(255) NOT NULL COMMENT '规则名称',
  `description` text COMMENT '规则描述',
  `source` varchar(50) NOT NULL COMMENT '规则来源(prometheus/elasticsearch)',
  `expression` text NOT NULL COMMENT '规则表达式',
  `level` varchar(20) NOT NULL COMMENT '告警级别(critical/warning/info)',
  `duration` int NOT NULL DEFAULT '0' COMMENT '持续时间(秒)',
  `labels` json DEFAULT NULL COMMENT '规则标签',
  `annotations` json DEFAULT NULL COMMENT '规则注释',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name_source` (`name`, `source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警规则表';

-- 告警通知表
CREATE TABLE IF NOT EXISTS `monitor_alert_notification` (
  `id` varchar(36) NOT NULL COMMENT '通知ID',
  `name` varchar(255) NOT NULL COMMENT '通知名称',
  `type` varchar(50) NOT NULL COMMENT '通知类型(email/webhook/kafka/sms)',
  `settings` json NOT NULL COMMENT '通知设置',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警通知表'; 