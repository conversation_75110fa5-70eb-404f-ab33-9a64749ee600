#!/bin/bash
# 导入告警表结构脚本

# 默认值
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-3306}
DB_NAME=${DB_NAME:-"dci"}
DB_USER=${DB_USER:-"root"}
DB_PASS=${DB_PASS:-""}

# 显示帮助信息
show_help() {
  echo "用法: $0 [选项]"
  echo "选项:"
  echo "  -h, --host      数据库主机 (默认: localhost)"
  echo "  -P, --port      数据库端口 (默认: 3306)"
  echo "  -d, --database  数据库名称 (默认: dci)"
  echo "  -u, --user      数据库用户 (默认: root)"
  echo "  -p, --password  数据库密码 (默认: 空)"
  echo "  --help          显示此帮助信息"
  echo ""
  echo "也可以通过环境变量设置: DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASS"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  key="$1"
  case $key in
    -h|--host)
      DB_HOST="$2"
      shift 2
      ;;
    -P|--port)
      DB_PORT="$2"
      shift 2
      ;;
    -d|--database)
      DB_NAME="$2"
      shift 2
      ;;
    -u|--user)
      DB_USER="$2"
      shift 2
      ;;
    -p|--password)
      DB_PASS="$2"
      shift 2
      ;;
    --help)
      show_help
      exit 0
      ;;
    *)
      echo "未知选项: $1"
      show_help
      exit 1
      ;;
  esac
done

# 脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ALERT_TABLES_SQL="${SCRIPT_DIR}/alert_tables.sql"

# 检查SQL文件是否存在
if [ ! -f "$ALERT_TABLES_SQL" ]; then
  echo "错误: 找不到告警表SQL文件: $ALERT_TABLES_SQL"
  exit 1
fi

echo "准备导入告警表结构到数据库 $DB_NAME..."

# 构建MySQL命令
if [ -z "$DB_PASS" ]; then
  # 无密码
  MYSQL_CMD="mysql -h$DB_HOST -P$DB_PORT -u$DB_USER $DB_NAME"
else
  # 有密码，使用环境变量传递密码，避免命令行明文显示
  export MYSQL_PWD="$DB_PASS"
  MYSQL_CMD="mysql -h$DB_HOST -P$DB_PORT -u$DB_USER $DB_NAME"
fi

# 执行SQL脚本
echo "正在导入告警表结构..."
$MYSQL_CMD < "$ALERT_TABLES_SQL"
RESULT=$?

# 清除环境变量中的密码
if [ ! -z "$DB_PASS" ]; then
  unset MYSQL_PWD
fi

# 检查执行结果
if [ $RESULT -eq 0 ]; then
  echo "告警表结构导入成功!"
else
  echo "告警表结构导入失败! 错误代码: $RESULT"
  exit 1
fi

echo "完成!" 