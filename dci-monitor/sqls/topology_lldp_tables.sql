-- =====================================================
-- 拓扑和LLDP相关表结构 - 整合版 (修改版: utf8mb4_general_ci)
-- =====================================================

-- ---------------------
-- 设备和端口基础信息表
-- ---------------------

-- 设备信息表，存储设备基本信息
CREATE TABLE IF NOT EXISTS `monitor_device_info` (
  `id` varchar(36) NOT NULL COMMENT '设备ID，UUID格式',
  `device_name` varchar(255) NOT NULL COMMENT '设备名称',
  `device_type` varchar(50) DEFAULT NULL COMMENT '设备类型',
  `manage_ip` varchar(45) DEFAULT NULL COMMENT '管理IP地址',
  `chassis_id` varchar(50) DEFAULT NULL COMMENT '设备底盘ID（通常是MAC地址）',
  `vendor` varchar(100) DEFAULT NULL COMMENT '设备厂商',
  `model` varchar(100) DEFAULT NULL COMMENT '设备型号',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1=正常，0=禁用',
  `last_seen` timestamp NULL DEFAULT NULL COMMENT '最后一次发现时间',
  `created_by` varchar(50) NOT NULL COMMENT '创建者',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_name` (`device_name`),
  KEY `idx_manage_ip` (`manage_ip`),
  KEY `idx_chassis_id` (`chassis_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='设备信息表';

-- 设备端口信息表，存储设备端口的详细信息
CREATE TABLE IF NOT EXISTS `monitor_device_port_info` (
  `id` varchar(36) NOT NULL COMMENT '记录ID，UUID格式',
  `device_id` varchar(36) NOT NULL COMMENT '设备ID',
  `if_index` int NOT NULL COMMENT '接口索引号(ifIndex)',
  `port_name` varchar(255) NOT NULL COMMENT '端口名称，如"10GE1/0/1"',
  `port_desc` varchar(512) DEFAULT NULL COMMENT '端口描述',
  `port_type` varchar(50) DEFAULT NULL COMMENT '端口类型',
  `admin_status` tinyint DEFAULT NULL COMMENT '管理状态: 1=up, 2=down',
  `oper_status` tinyint DEFAULT NULL COMMENT '运行状态: 1=up, 2=down',
  `mac_address` varchar(17) DEFAULT NULL COMMENT '端口MAC地址',
  `speed` bigint DEFAULT NULL COMMENT '端口速率(bps)',
  `mtu` int DEFAULT NULL COMMENT '最大传输单元(字节)',
  `is_physical` tinyint(1) DEFAULT 1 COMMENT '是否物理端口: 1=是, 0=否(逻辑接口)',
  `is_lldp_enabled` tinyint(1) DEFAULT NULL COMMENT '是否启用LLDP: 1=是, 0=否',
  `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `raw_data` json DEFAULT NULL COMMENT '原始采集数据，JSON格式',
  `created_by` varchar(50) NOT NULL COMMENT '创建者',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_ifindex` (`device_id`,`if_index`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_last_updated` (`last_updated`),
  KEY `idx_port_name` (`port_name`),
  KEY `idx_mac_address` (`mac_address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='设备端口信息表';

-- ----------------------
-- 标识符映射相关表
-- ----------------------

-- 设备标识符映射表，用于存储不同类型标识符到系统设备ID的映射关系
CREATE TABLE IF NOT EXISTS `monitor_device_identifier_mapping` (
  `id` varchar(36) NOT NULL COMMENT '记录ID，UUID格式',
  `device_id` varchar(36) NOT NULL COMMENT '系统中的标准设备ID',
  `identifier_type` varchar(50) NOT NULL COMMENT '标识符类型：name(设备名称)、chassis_id(机箱ID/MAC地址)、ip(管理IP)等',
  `identifier_value` varchar(255) NOT NULL COMMENT '标识符值',
  `priority` int NOT NULL DEFAULT 0 COMMENT '标识符优先级，数值越小优先级越高，用于解决冲突',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否活跃：1=活跃，0=不活跃',
  `last_seen` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后一次发现时间',
  `created_by` varchar(50) NOT NULL COMMENT '创建者',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_type_value` (`identifier_type`, `identifier_value`(200)),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_last_seen` (`last_seen`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='设备标识符映射表';

-- 端口标识符映射表，用于存储不同类型端口标识符到系统端口ID的映射关系
CREATE TABLE IF NOT EXISTS `monitor_port_identifier_mapping` (
  `id` varchar(36) NOT NULL COMMENT '记录ID，UUID格式',
  `device_id` varchar(36) NOT NULL COMMENT '所属设备ID',
  `port_id` varchar(36) NOT NULL COMMENT '系统中的标准端口ID',
  `identifier_type` varchar(50) NOT NULL COMMENT '标识符类型：name(端口名称)、index(ifIndex)、desc(描述)等',
  `identifier_value` varchar(255) NOT NULL COMMENT '标识符值',
  `priority` int NOT NULL DEFAULT 0 COMMENT '标识符优先级，数值越小优先级越高',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否活跃：1=活跃，0=不活跃',
  `last_seen` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后一次发现时间',
  `created_by` varchar(50) NOT NULL COMMENT '创建者',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_type_value` (`device_id`, `identifier_type`, `identifier_value`(150)),
  KEY `idx_port_id` (`port_id`),
  KEY `idx_last_seen` (`last_seen`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='端口标识符映射表';

-- ----------------------
-- LLDP相关表
-- ----------------------

-- LLDP邻居关系缓存表，用于临时存储原始LLDP邻居关系信息，避免重复处理
CREATE TABLE IF NOT EXISTS `monitor_lldp_neighbor_cache` (
  `id` varchar(36) NOT NULL COMMENT '记录ID，UUID格式',
  `local_device_identifier` varchar(255) NOT NULL COMMENT '本地设备标识符',
  `local_device_id_type` varchar(50) NOT NULL COMMENT '本地设备标识符类型',
  `local_port_index` varchar(50) NOT NULL COMMENT '本地端口索引',
  `remote_chassis_id` varchar(255) NOT NULL COMMENT '远端设备机箱ID',
  `remote_port_id` varchar(255) NOT NULL COMMENT '远端设备端口ID',
  `remote_sysname` varchar(255) DEFAULT NULL COMMENT '远端设备名称',
  `mapped_local_device_id` varchar(36) DEFAULT NULL COMMENT '映射后的本地设备ID',
  `mapped_local_port_id` varchar(36) DEFAULT NULL COMMENT '映射后的本地端口ID',
  `mapped_remote_device_id` varchar(36) DEFAULT NULL COMMENT '映射后的远端设备ID',
  `mapped_remote_port_id` varchar(36) DEFAULT NULL COMMENT '映射后的远端端口ID',
  `mapping_status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '映射状态：PENDING(待处理)、MAPPED(已映射)、PARTIAL(部分映射)、FAILED(映射失败)',
  `raw_data` json DEFAULT NULL COMMENT '原始LLDP数据，JSON格式',
  `last_seen` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后一次发现时间',
  `error_message` varchar(512) DEFAULT NULL COMMENT '映射失败时的错误信息',
  `created_by` varchar(50) NOT NULL COMMENT '创建者',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_local_remote` (`local_device_identifier`(100), `local_port_index`, `remote_chassis_id`(100), `remote_port_id`(100)),
  KEY `idx_local_device` (`local_device_identifier`(100)),
  KEY `idx_remote_chassis` (`remote_chassis_id`(100)),
  KEY `idx_last_seen` (`last_seen`),
  KEY `idx_mapping_status` (`mapping_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='LLDP邻居关系缓存表';

-- ----------------------
-- 拓扑相关表
-- ----------------------

-- 拓扑快照表，存储拓扑图的快照信息
CREATE TABLE IF NOT EXISTS `monitor_topology_snapshot` (
  `id` varchar(36) NOT NULL COMMENT '快照ID，UUID格式',
  `name` varchar(255) NOT NULL COMMENT '快照名称',
  `description` varchar(1000) DEFAULT NULL COMMENT '快照描述',
  `node_count` int NOT NULL DEFAULT '0' COMMENT '节点数量',
  `edge_count` int NOT NULL DEFAULT '0' COMMENT '边数量',
  `is_current` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为当前活动快照',
  `created_by` varchar(50) NOT NULL COMMENT '创建者',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_is_current` (`is_current`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='拓扑快照元数据表';

-- 拓扑节点表，存储拓扑图中的节点信息
CREATE TABLE IF NOT EXISTS `monitor_topology_node` (
  `id` varchar(36) NOT NULL COMMENT '节点ID，UUID格式',
  `snapshot_id` varchar(36) NOT NULL COMMENT '关联的快照ID',
  `device_id` varchar(36) NOT NULL COMMENT '设备ID',
  `node_type` varchar(50) NOT NULL COMMENT '节点类型',
  `properties` json DEFAULT NULL COMMENT '节点属性JSON',
  `created_by` varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建者',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_snapshot_id` (`snapshot_id`),
  KEY `idx_device_id` (`device_id`),
  CONSTRAINT `fk_node_snapshot` FOREIGN KEY (`snapshot_id`) REFERENCES `monitor_topology_snapshot` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='拓扑节点数据表';

-- 拓扑边表，存储拓扑图中的连接关系
CREATE TABLE IF NOT EXISTS `monitor_topology_edge` (
  `id` varchar(36) NOT NULL COMMENT '边ID，UUID格式',
  `snapshot_id` varchar(36) NOT NULL COMMENT '关联的快照ID',
  `source_node_id` varchar(36) NOT NULL COMMENT '源节点ID',
  `target_node_id` varchar(36) NOT NULL COMMENT '目标节点ID',
  `source_port_id` varchar(36) DEFAULT NULL COMMENT '源端口ID',
  `target_port_id` varchar(36) DEFAULT NULL COMMENT '目标端口ID',
  `edge_type` varchar(50) NOT NULL COMMENT '边类型',
  `properties` json DEFAULT NULL COMMENT '边属性JSON',
  `created_by` varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建者',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_snapshot_id` (`snapshot_id`),
  KEY `idx_source_node_id` (`source_node_id`),
  KEY `idx_target_node_id` (`target_node_id`),
  CONSTRAINT `fk_edge_snapshot` FOREIGN KEY (`snapshot_id`) REFERENCES `monitor_topology_snapshot` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='拓扑边数据表';

-- ----------------------
-- 可选的外键约束（取消注释启用）
-- ----------------------

-- ALTER TABLE `monitor_topology_edge` ADD CONSTRAINT `fk_edge_source_node` FOREIGN KEY (`source_node_id`) REFERENCES `monitor_topology_node` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
-- ALTER TABLE `monitor_topology_edge` ADD CONSTRAINT `fk_edge_target_node` FOREIGN KEY (`target_node_id`) REFERENCES `monitor_topology_node` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
-- ALTER TABLE `monitor_device_identifier_mapping` ADD CONSTRAINT `fk_device_id` FOREIGN KEY (`device_id`) REFERENCES `dci_device` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
-- ALTER TABLE `monitor_port_identifier_mapping` ADD CONSTRAINT `fk_port_device` FOREIGN KEY (`device_id`) REFERENCES `dci_device` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
-- ALTER TABLE `monitor_port_identifier_mapping` ADD CONSTRAINT `fk_port_id` FOREIGN KEY (`port_id`) REFERENCES `dci_logic_port` (`id`) ON DELETE CASCADE ON UPDATE CASCADE; 