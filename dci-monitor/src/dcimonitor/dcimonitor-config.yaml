---
apiVersion: v1
kind: ConfigMap
metadata:
  name: dcimonitor-config
  namespace: dci
data:
  config.yaml: |
    # DCI监控系统通用配置文件
    # Credentials are injected via environment variables from secrets for security.

    database:
      driver: mysql
      host: "rm-el5i5532025ja5639.mysql.rds.pcloud-ops.citic.com"
      port: 3306
      # username & password will be injected from secret 'dcimonitor-credentials'
      username: root
      password: FJkhJ0GBPKZva1w
      dbname: dci
      charset: utf8mb4

    kafka:
      brokers:
        - "kafka-0.kafka-headless.dci.svc.cluster.local:30002"
        - "kafka-1.kafka-headless.dci.svc.cluster.local:30002"
        - "kafka-2.kafka-headless.dci.svc.cluster.local:30002"
      security:
        tls:
          enabled: true
          certFile: "/etc/kafka/certs/dci-monitor/dci-monitor.client.crt"
          keyFile: "/etc/kafka/certs/dci-monitor/dci-monitor.client.key"
          caFile: "/etc/kafka/certs/dci-monitor/intermediate-ca.crt"
        sasl:
          enabled: true
          mechanism: PLAIN
          # username & password will be injected from secret 'dcimonitor-credentials'
      topics:
        rawTopology: dci.monitor.v1.defaultchannel.topology.lldp
        rawMetrics: dci.monitor.v1.defaultchannel.metrics.telegraf
        rawLogs: dci.monitor.v1.defaultchannel.logs.syslog
        rawTasks: dci.monitor.v1.defaultchannel.tasks.control
        processedTopology: dci.monitor.v1.defaultchannel.topology.processed
        processedEvents: dci.monitor.v1.defaultchannel.events
      consumerGroups:
        topologyProcessor: dci-topology-processor
        metricsProcessor: dci-metrics-processor
        logsProcessor: dci-logs-processor

    kafkaInput:
      brokers:
        - "kafka-0.kafka-headless.dci.svc.cluster.local:9093"
        - "kafka-1.kafka-headless.dci.svc.cluster.local:9093"
        - "kafka-2.kafka-headless.dci.svc.cluster.local:9093"
      consumerGroup: dci-topology-processor
      topics:
        - dci.monitor.v1.defaultchannel.topology.lldp
      autoCommit: true
      commitInterval: 5s
      initialOffset: newest
      tls:
        enabled: true
        certFile: "/etc/kafka/certs/dci-monitor/dci-monitor.client.crt"
        keyFile: "/etc/kafka/certs/dci-monitor/dci-monitor.client.key"
        caFile: "/etc/kafka/certs/dci-monitor/intermediate-ca.crt"
      sasl:
        enabled: true
        mechanism: PLAIN

    kafkaOutput:
      brokers:
        - "kafka-0.kafka-headless.dci.svc.cluster.local:9093"
        - "kafka-1.kafka-headless.dci.svc.cluster.local:9093"
        - "kafka-2.kafka-headless.dci.svc.cluster.local:9093"
      requiredAcks: local
      compression: none
      flushInterval: 1s
      tls:
        enabled: true
        certFile: "/etc/kafka/certs/dci-monitor/dci-monitor.client.crt"
        keyFile: "/etc/kafka/certs/dci-monitor/dci-monitor.client.key"
        caFile: "/etc/kafka/certs/dci-monitor/intermediate-ca.crt"
      sasl:
        enabled: true
        mechanism: PLAIN

    topology:
      snapshotInterval: 1h
      healthCheckInterval: 30s

    server:
      port: 8080
      mode: "release" # Use 'release' in production to disable Swagger UI

    logger:
      level: "debug" # debug, info, warn, error
      dir: "/var/log/dcimonitor"
      maxSize: 100
      maxBackups: 30
      maxAge: 30
      compress: true

    prometheus:
      address: "http://prometheus.dci.svc.cluster.local:9090" # Prometheus 服务器地址
      timeout: 30s # 查询超时时间
      username: "dciadmin"
      password: "c8PI3huRud8tW"
      rule_file: "/etc/prometheus/rules/dci_alerts.yml" # 告警规则文件路径
      reload_enabled: true # 是否启用重新加载功能

    alertmanager:
      address: "http://alertmanager.dci.svc.cluster.local:9093" # AlertManager 服务器地址
      webhook_auth_token: "dci-webhook-secret-token-2025" # Webhook认证令牌 