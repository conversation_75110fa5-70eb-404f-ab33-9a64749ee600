package alert

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"
)

// Alert 定义了告警记录的数据结构
type Alert struct {
	ID                string     `json:"id" db:"id"`
	Name              string     `json:"name" db:"name"`
	Source            string     `json:"source" db:"source"`
	Level             string     `json:"level" db:"level"`
	Status            string     `json:"status" db:"status"`
	Description       string     `json:"description" db:"description"`
	Labels            JSONMap    `json:"labels" db:"labels"`
	Annotations       JSONMap    `json:"annotations" db:"annotations"`
	StartsAt          time.Time  `json:"starts_at" db:"starts_at"`
	EndsAt            *time.Time `json:"ends_at,omitempty" db:"ends_at"`
	NotificationDueAt *time.Time `json:"notification_due_at,omitempty" db:"notification_due_at"`
	ResolvedAt        *time.Time `json:"resolved_at,omitempty" db:"resolved_at"`
	UpdatedAt         time.Time  `json:"updated_at" db:"updated_at"`
	GeneratorURL      string     `json:"generator_url,omitempty" db:"generator_url"`
	RuleID            string     `json:"rule_id,omitempty" db:"rule_id"`
	AcknowledgedBy    string     `json:"acknowledged_by,omitempty" db:"acknowledged_by"`
	AcknowledgedAt    *time.Time `json:"acknowledged_at,omitempty" db:"acknowledged_at"`
	Note              string     `json:"note,omitempty" db:"note"`
}

// TableName 返回Alert对应的数据库表名
func (Alert) TableName() string {
	return "monitor_alert"
}

// AlertRule 定义了告警规则的数据结构
type AlertRule struct {
	ID          string    `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	Description string    `json:"description" db:"description"`
	Source      string    `json:"source" db:"source"`
	Expression  string    `json:"expression" db:"expression"`
	Level       string    `json:"level" db:"level"`
	Duration    int       `json:"duration" db:"duration"`
	Labels      JSONMap   `json:"labels" db:"labels"`
	Annotations JSONMap   `json:"annotations" db:"annotations"`
	Enabled     bool      `json:"enabled" db:"enabled"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// TableName 返回AlertRule对应的数据库表名
func (AlertRule) TableName() string {
	return "monitor_alert_rule"
}

// AlertNotification 定义了告警通知渠道的数据结构
type AlertNotification struct {
	ID        string    `json:"id" db:"id"`
	Name      string    `json:"name" db:"name"`
	Type      string    `json:"type" db:"type"`
	Settings  JSONMap   `json:"settings" db:"settings"`
	Enabled   bool      `json:"enabled" db:"enabled"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}

// TableName 返回AlertNotification对应的数据库表名
func (AlertNotification) TableName() string {
	return "monitor_alert_notification"
}

// JSONMap 是一个用于存储JSON数据的映射类型
type JSONMap map[string]string

// Value 实现了driver.Valuer接口，用于将JSONMap转换为数据库值
func (m JSONMap) Value() (driver.Value, error) {
	if m == nil {
		return nil, nil
	}
	return json.Marshal(m)
}

// Scan 实现了sql.Scanner接口，用于将数据库值转换为JSONMap
func (m *JSONMap) Scan(value interface{}) error {
	if value == nil {
		*m = make(JSONMap)
		return nil
	}

	var data []byte
	switch v := value.(type) {
	case string:
		data = []byte(v)
	case []byte:
		data = v
	default:
		return errors.New("不支持的类型")
	}

	if len(data) == 0 {
		*m = make(JSONMap)
		return nil
	}

	return json.Unmarshal(data, m)
}

// PrometheusWebhookAlert 定义了从Prometheus接收的告警数据结构
type PrometheusWebhookAlert struct {
	Status       string            `json:"status"`
	Labels       map[string]string `json:"labels"`
	Annotations  map[string]string `json:"annotations"`
	StartsAt     time.Time         `json:"startsAt"`
	EndsAt       time.Time         `json:"endsAt"`
	GeneratorURL string            `json:"generatorURL"`
}

// PrometheusWebhookRequest 定义了Prometheus告警Webhook的请求数据结构
type PrometheusWebhookRequest struct {
	Version           string                   `json:"version"`
	GroupKey          string                   `json:"groupKey"`
	Status            string                   `json:"status"`
	Receiver          string                   `json:"receiver"`
	GroupLabels       map[string]string        `json:"groupLabels"`
	CommonLabels      map[string]string        `json:"commonLabels"`
	CommonAnnotations map[string]string        `json:"commonAnnotations"`
	ExternalURL       string                   `json:"externalURL"`
	Alerts            []PrometheusWebhookAlert `json:"alerts"`
}

// AlertAcknowledgeRequest 定义了确认告警的请求数据结构
type AlertAcknowledgeRequest struct {
	// 告警确认人，必填字段，表示谁在处理此告警
	AcknowledgedBy string `json:"acknowledged_by" binding:"required" example:"张三"`
	// 处理备注，选填，可以添加处理进展或计划
	Note string `json:"note,omitempty" example:"正在联系网络组排查原因"`
}

// AlertRuleToggleRequest 定义了启用/禁用告警规则的请求数据结构
type AlertRuleToggleRequest struct {
	Enabled bool `json:"enabled" binding:"required"` // 是否启用
}

// SuccessResponse 定义了成功响应的数据结构
type SuccessResponse struct {
	RequestID string `json:"request_id"` // 请求ID
	Message   string `json:"message"`    // 成功消息
}

// ErrorResponse 定义了错误响应的数据结构
type ErrorResponse struct {
	RequestID string `json:"request_id"`        // 请求ID
	Error     string `json:"error"`             // 错误信息
	Details   string `json:"details,omitempty"` // 错误详情
}

// PaginatedResponse 定义了分页响应的数据结构
type PaginatedResponse struct {
	RequestID string      `json:"request_id"`         // 请求ID
	Total     int         `json:"total"`              // 总记录数
	Page      int         `json:"page"`               // 当前页码
	PageSize  int         `json:"page_size"`          // 每页记录数
	Data      interface{} `json:"data"`               // 数据
	HasMore   bool        `json:"has_more,omitempty"` // 是否有更多数据
}

// Response 定义了通用响应的数据结构
type Response struct {
	Code int         `json:"code"` // 状态码
	Data interface{} `json:"data"` // 响应数据
}

// AlertListResponse 定义了告警列表响应的数据结构
type AlertListResponse struct {
	Alerts    []Alert `json:"alerts"`     // 告警列表
	Page      int     `json:"page"`       // 当前页码
	PageCount int     `json:"page_count"` // 总页数
	PageSize  int     `json:"page_size"`  // 每页数量
	Total     int     `json:"total"`      // 总记录数
}

// AlertAcknowledgeResponse 定义了告警确认响应的数据结构
type AlertAcknowledgeResponse struct {
	Code    int    `json:"code" example:"200"`      // 状态码
	Message string `json:"message" example:"告警已确认"` // 响应消息
}
