package alert

import (
	"crypto/md5"
	"dcimonitor/internal/middleware"
	"dcimonitor/internal/models"
	"dcimonitor/internal/utils/timeutil"
	"encoding/hex"
	"fmt"
	"net/http"
	"sort"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Service 提供告警相关的服务功能
type Service struct {
	dao    *DAO
	logger *zap.Logger
}

// NewService 创建一个新的Service实例
func NewService(dao *DAO, logger *zap.Logger) *Service {
	return &Service{
		dao:    dao,
		logger: logger,
	}
}

// GenerateAlertFingerprint 根据告警标签生成唯一指纹
func (s *Service) GenerateAlertFingerprint(labels map[string]string) string {
	// 对标签键进行排序，确保相同的标签集合生成相同的指纹
	keys := make([]string, 0, len(labels))
	for k := range labels {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 构建标签字符串
	var sb strings.Builder
	for _, k := range keys {
		// 跳过与告警状态相关的临时标签
		if k == "alertstate" {
			continue
		}
		sb.WriteString(k)
		sb.WriteString("=")
		sb.WriteString(labels[k])
		sb.WriteString(";")
	}
	s.logger.Info("生成告警指纹", zap.String("Fingerprint", sb.String()))

	// 生成MD5哈希
	hash := md5.Sum([]byte(sb.String()))
	return hex.EncodeToString(hash[:])
}

// ProcessPrometheusWebhook 处理来自Prometheus AlertManager的webhook请求
func (s *Service) ProcessPrometheusWebhook(c *gin.Context) (*Response, error) {
	requestID := middleware.GetRequestID(c)

	// 解析请求体
	var req PrometheusWebhookRequest
	if err := c.BindJSON(&req); err != nil {
		s.logger.Warn("解析Prometheus告警webhook失败",
			zap.Error(err),
			zap.String("request_id", requestID))
		return &Response{
			Code: 400,
			Data: models.ErrorResponse{
				Error:     "无效的请求格式",
				RequestID: requestID,
			},
		}, err
	}

	s.logger.Info("收到Prometheus告警webhook",
		zap.String("status", req.Status),
		zap.Int("alerts_count", len(req.Alerts)),
		zap.String("request_id", requestID),
	)

	// 处理每个告警
	for _, promAlert := range req.Alerts {
		// 构建告警实体
		alertLabels := make(map[string]string)
		for k, v := range promAlert.Labels {
			alertLabels[k] = v
		}

		// 添加告警指纹
		fingerprint := s.GenerateAlertFingerprint(alertLabels)
		alertLabels["fingerprint"] = fingerprint

		// 构建告警注释
		alertAnnotations := make(map[string]string)
		for k, v := range promAlert.Annotations {
			alertAnnotations[k] = v
		}

		// 创建告警对象
		alert := &Alert{
			Name:         promAlert.Labels["alertname"],
			Source:       "prometheus",
			Level:        promAlert.Labels["severity"],
			Status:       promAlert.Status,
			Description:  promAlert.Annotations["description"],
			Labels:       alertLabels,
			Annotations:  alertAnnotations,
			StartsAt:     timeutil.ToLocalTime(promAlert.StartsAt), // 使用timeutil转换为本地时区
			GeneratorURL: promAlert.GeneratorURL,
		}

		// 处理告警状态和时间字段
		existingAlert, err := s.dao.GetAlertByFingerprintAndSource(fingerprint, "prometheus")
		if err != nil {
			s.logger.Error("查询现有告警失败", zap.Error(err), zap.String("fingerprint", fingerprint))
			continue
		}

		// 当前本地时间
		now := timeutil.NowInLocalTime() // 使用timeutil获取当前本地时区时间

		// 新告警
		if existingAlert == nil {
			alert.Status = "firing"

			if promAlert.Status == "firing" {
				// 对于未解决的告警，检查EndsAt是否为有效时间
				notificationDueAt := timeutil.ToLocalTime(promAlert.EndsAt) // 使用timeutil转换为本地时区

				// 检查转换后的时间是否有效（不是零值且不是很早的日期）
				if !notificationDueAt.IsZero() && notificationDueAt.Year() > 1970 {
					alert.NotificationDueAt = &notificationDueAt
					s.logger.Debug("设置告警通知截止时间",
						zap.String("fingerprint", fingerprint),
						zap.Time("notification_due_at", notificationDueAt))
				} else {
					// 如果没有有效的通知截止时间，设置为当前时间加1小时
					defaultDueTime := now.Add(1 * time.Hour)
					alert.NotificationDueAt = &defaultDueTime
					s.logger.Debug("使用默认通知截止时间",
						zap.String("fingerprint", fingerprint),
						zap.Time("notification_due_at", defaultDueTime))
				}
				// alert.EndsAt保持为nil，因为告警尚未解决
			} else if promAlert.Status == "resolved" {
				// 对于已解决的告警，设置EndsAt和ResolvedAt
				endsAt := now
				resolvedAt := now
				alert.EndsAt = &endsAt
				alert.ResolvedAt = &resolvedAt
			}

			alert.UpdatedAt = now

			// 创建新告警
			if err := s.dao.CreateAlert(alert); err != nil {
				s.logger.Error("创建告警失败", zap.Error(err), zap.String("alertname", alert.Name))
				continue
			}

			s.logger.Info("创建新告警",
				zap.String("alert_id", alert.ID),
				zap.String("name", alert.Name),
				zap.String("fingerprint", fingerprint))
		} else {
			// 更新已存在的告警
			alert.ID = existingAlert.ID

			// 更新告警状态
			switch promAlert.Status {
			case "resolved":
				// 已解决告警
				alert.Status = "resolved"
				endsAt := now
				resolvedAt := now
				alert.EndsAt = &endsAt
				alert.ResolvedAt = &resolvedAt
				alert.NotificationDueAt = nil // 清空通知截止时间，因为已解决

			case "firing":
				// 告警仍在触发
				if existingAlert.Status == "acknowledged" {
					// 保留确认状态
					alert.Status = "acknowledged"
					alert.AcknowledgedBy = existingAlert.AcknowledgedBy
					alert.AcknowledgedAt = existingAlert.AcknowledgedAt
					alert.Note = existingAlert.Note
				} else {
					alert.Status = "firing"
				}

				// 保留原始触发时间
				alert.StartsAt = existingAlert.StartsAt

				// 更新通知截止时间
				notificationDueAt := timeutil.ToLocalTime(promAlert.EndsAt) // 使用timeutil转换为本地时区

				// 检查转换后的时间是否有效（不是零值且不是很早的日期）
				if !notificationDueAt.IsZero() && notificationDueAt.Year() > 1970 {
					alert.NotificationDueAt = &notificationDueAt
					s.logger.Debug("更新告警通知截止时间",
						zap.String("alert_id", alert.ID),
						zap.Time("notification_due_at", notificationDueAt))
				} else {
					// 如果没有有效的通知截止时间，设置为当前时间加1小时
					defaultDueTime := now.Add(1 * time.Hour)
					alert.NotificationDueAt = &defaultDueTime
					s.logger.Debug("使用默认通知截止时间",
						zap.String("alert_id", alert.ID),
						zap.Time("notification_due_at", defaultDueTime))
				}
				alert.EndsAt = nil // 清空EndsAt，因为告警尚未解决
			}

			alert.UpdatedAt = now

			// 更新现有告警
			if err := s.dao.UpdateAlert(alert); err != nil {
				s.logger.Error("更新告警失败", zap.Error(err), zap.String("alert_id", alert.ID))
				continue
			}

			s.logger.Info("更新现有告警",
				zap.String("alert_id", alert.ID),
				zap.String("name", alert.Name),
				zap.String("status", alert.Status),
				zap.String("fingerprint", fingerprint))
		}
	}

	return &Response{
		Code: 200,
		Data: models.SuccessResponse{
			Message:   fmt.Sprintf("成功处理 %d 条告警", len(req.Alerts)),
			RequestID: requestID,
		},
	}, nil
}

// ListAlerts 获取告警列表
func (s *Service) ListAlerts(c *gin.Context) (*Response, error) {
	requestID := middleware.GetRequestID(c)

	// 获取查询参数
	source := c.Query("source")
	level := c.Query("level")
	status := c.Query("status")
	deviceID := c.Query("device_id")
	page := 1
	pageSize := 20

	if pageStr := c.Query("page"); pageStr != "" {
		fmt.Sscanf(pageStr, "%d", &page)
		if page < 1 {
			page = 1
		}
	}

	if pageSizeStr := c.Query("page_size"); pageSizeStr != "" {
		fmt.Sscanf(pageSizeStr, "%d", &pageSize)
		if pageSize < 1 {
			pageSize = 1
		} else if pageSize > 100 {
			pageSize = 100
		}
	}

	// 构建过滤条件
	filters := make(map[string]interface{})
	if source != "" {
		filters["source"] = source
	}
	if level != "" {
		filters["level"] = level
	}
	if status != "" {
		filters["status"] = status
	}
	if deviceID != "" {
		filters["device_id"] = deviceID
	}

	// 获取总记录数
	total, err := s.dao.CountAlerts(filters)
	if err != nil {
		s.logger.Error("计算告警记录数量失败",
			zap.Error(err),
			zap.String("request_id", requestID))
		return &Response{
			Code: 500,
			Data: models.ErrorResponse{
				Error:     "获取告警记录数量失败",
				RequestID: requestID,
			},
		}, err
	}

	// 计算分页信息
	offset := (page - 1) * pageSize
	pageCount := (total + pageSize - 1) / pageSize

	// 获取告警列表
	alerts, err := s.dao.ListAlerts(filters, pageSize, offset)
	if err != nil {
		s.logger.Error("获取告警列表失败",
			zap.Error(err),
			zap.String("request_id", requestID))
		return &Response{
			Code: 500,
			Data: models.ErrorResponse{
				Error:     "获取告警列表失败",
				RequestID: requestID,
			},
		}, err
	}

	// 转换为响应结构
	result := make([]Alert, len(alerts))
	for i, alert := range alerts {
		result[i] = *alert
	}

	return &Response{
		Code: 200,
		Data: AlertListResponse{
			Alerts:    result,
			Page:      page,
			PageSize:  pageSize,
			PageCount: pageCount,
			Total:     total,
		},
	}, nil
}

// GetAlertByID 根据ID获取告警详情
func (s *Service) GetAlertByID(c *gin.Context) (*Response, error) {
	id := c.Param("id")
	requestID := middleware.GetRequestID(c)

	alert, err := s.dao.GetAlertByID(id)
	if err != nil {
		s.logger.Error("获取告警失败",
			zap.Error(err),
			zap.String("alert_id", id),
			zap.String("request_id", requestID))
		return &Response{
			Code: http.StatusInternalServerError,
			Data: models.ErrorResponse{
				Error:     "获取告警失败: " + err.Error(),
				RequestID: requestID,
			},
		}, nil
	}

	if alert == nil {
		s.logger.Warn("告警不存在",
			zap.String("alert_id", id),
			zap.String("request_id", requestID))
		return &Response{
			Code: http.StatusNotFound,
			Data: models.ErrorResponse{
				Error:     "告警不存在",
				RequestID: requestID,
			},
		}, nil
	}

	// 记录成功日志
	s.logger.Debug("成功获取告警详情",
		zap.String("alert_id", id),
		zap.String("request_id", requestID))

	return &Response{
		Code: http.StatusOK,
		Data: alert,
	}, nil
}

// AcknowledgeAlert 确认告警
func (s *Service) AcknowledgeAlert(c *gin.Context) (*Response, error) {
	requestID := middleware.GetRequestID(c)

	id := c.Param("id")
	if id == "" {
		s.logger.Warn("缺少告警ID", zap.String("request_id", requestID))
		return &Response{
			Code: 400,
			Data: models.ErrorResponse{
				Error:     "缺少告警ID",
				RequestID: requestID,
			},
		}, fmt.Errorf("缺少告警ID")
	}

	// 获取请求体
	var req AlertAcknowledgeRequest
	if err := c.BindJSON(&req); err != nil {
		s.logger.Warn("解析确认告警请求失败",
			zap.Error(err),
			zap.String("request_id", requestID))
		return &Response{
			Code: 400,
			Data: models.ErrorResponse{
				Error:     "无效的请求格式",
				RequestID: requestID,
			},
		}, err
	}

	// 获取告警详情
	alert, err := s.dao.GetAlertByID(id)
	if err != nil {
		s.logger.Error("获取告警详情失败",
			zap.Error(err),
			zap.String("alert_id", id),
			zap.String("request_id", requestID))
		return &Response{
			Code: 500,
			Data: models.ErrorResponse{
				Error:     "获取告警详情失败",
				RequestID: requestID,
			},
		}, err
	}

	if alert == nil {
		s.logger.Warn("告警不存在",
			zap.String("alert_id", id),
			zap.String("request_id", requestID))
		return &Response{
			Code: 404,
			Data: models.ErrorResponse{
				Error:     "告警不存在",
				RequestID: requestID,
			},
		}, fmt.Errorf("告警不存在")
	}

	// 检查告警状态
	if alert.Status == "resolved" {
		s.logger.Warn("已解决的告警不能确认",
			zap.String("alert_id", id),
			zap.String("request_id", requestID))
		return &Response{
			Code: 400,
			Data: models.ErrorResponse{
				Error:     "已解决的告警不能确认",
				RequestID: requestID,
			},
		}, fmt.Errorf("已解决的告警不能确认")
	}

	if alert.Status == "acknowledged" {
		s.logger.Warn("告警已被确认",
			zap.String("alert_id", id),
			zap.String("request_id", requestID))
		return &Response{
			Code: 400,
			Data: models.ErrorResponse{
				Error:     "告警已被确认",
				RequestID: requestID,
			},
		}, fmt.Errorf("告警已被确认")
	}

	// 确认告警
	err = s.dao.AcknowledgeAlert(id, req.AcknowledgedBy, req.Note)
	if err != nil {
		s.logger.Error("确认告警失败",
			zap.Error(err),
			zap.String("alert_id", id),
			zap.String("request_id", requestID))
		return &Response{
			Code: 500,
			Data: models.ErrorResponse{
				Error:     "确认告警失败",
				RequestID: requestID,
			},
		}, err
	}

	s.logger.Info("告警已确认",
		zap.String("alert_id", id),
		zap.String("acknowledged_by", req.AcknowledgedBy),
		zap.String("request_id", requestID),
	)

	return &Response{
		Code: 200,
		Data: models.SuccessResponse{
			Message:   "告警已确认",
			RequestID: requestID,
		},
	}, nil
}

// GetAlertStatistics 获取告警统计信息
func (s *Service) GetAlertStatistics(c *gin.Context) (*Response, error) {
	requestID := middleware.GetRequestID(c)

	// 统计不同状态的告警数量
	statuses := []string{"firing", "acknowledged", "resolved"}
	statusCounts := make(map[string]int)

	for _, status := range statuses {
		filters := map[string]interface{}{"status": status}
		count, err := s.dao.CountAlerts(filters)
		if err != nil {
			s.logger.Error("统计告警数量失败",
				zap.Error(err),
				zap.String("status", status),
				zap.String("request_id", requestID))
			return &Response{
				Code: 500,
				Data: models.ErrorResponse{
					Error:     "获取告警统计信息失败",
					RequestID: requestID,
				},
			}, err
		}
		statusCounts[status] = count
	}

	// 统计不同级别的活跃告警数量（firing + acknowledged）
	levels := []string{"critical", "warning", "info"}
	levelCounts := make(map[string]int)

	for _, level := range levels {
		// 统计firing状态的告警
		firingFilters := map[string]interface{}{"level": level, "status": "firing"}
		firingCount, err := s.dao.CountAlerts(firingFilters)
		if err != nil {
			s.logger.Error("统计告警数量失败",
				zap.Error(err),
				zap.String("level", level),
				zap.String("status", "firing"),
				zap.String("request_id", requestID))
			return &Response{
				Code: 500,
				Data: models.ErrorResponse{
					Error:     "获取告警统计信息失败",
					RequestID: requestID,
				},
			}, err
		}

		// 统计acknowledged状态的告警
		ackFilters := map[string]interface{}{"level": level, "status": "acknowledged"}
		ackCount, err := s.dao.CountAlerts(ackFilters)
		if err != nil {
			s.logger.Error("统计告警数量失败",
				zap.Error(err),
				zap.String("level", level),
				zap.String("status", "acknowledged"),
				zap.String("request_id", requestID))
			return &Response{
				Code: 500,
				Data: models.ErrorResponse{
					Error:     "获取告警统计信息失败",
					RequestID: requestID,
				},
			}, err
		}

		levelCounts[level] = firingCount + ackCount
	}

	// 统计最近24小时内的告警数量
	now := time.Now()
	oneDayAgo := now.Add(-24 * time.Hour)

	recentFilters := map[string]interface{}{"start_time": oneDayAgo}
	recentCount, err := s.dao.CountAlerts(recentFilters)
	if err != nil {
		s.logger.Error("统计最近告警数量失败",
			zap.Error(err),
			zap.String("request_id", requestID))
		return &Response{
			Code: 500,
			Data: models.ErrorResponse{
				Error:     "获取告警统计信息失败",
				RequestID: requestID,
			},
		}, err
	}

	// 构建响应
	return &Response{
		Code: 200,
		Data: map[string]interface{}{
			"status_counts": statusCounts,
			"level_counts":  levelCounts,
			"recent_24h":    recentCount,
			"total":         statusCounts["firing"] + statusCounts["acknowledged"] + statusCounts["resolved"],
			"active":        statusCounts["firing"] + statusCounts["acknowledged"],
			"request_id":    requestID,
		},
	}, nil
}

// ListAlertRules 获取告警规则列表
func (s *Service) ListAlertRules(c *gin.Context) (*Response, error) {
	requestID := middleware.GetRequestID(c)

	// 获取查询参数
	source := c.Query("source")
	enabledStr := c.Query("enabled")

	var enabled *bool
	if enabledStr != "" {
		enabledVal := enabledStr == "true"
		enabled = &enabledVal
	}

	// 获取规则列表
	rules, err := s.dao.ListAlertRules(source, enabled)
	if err != nil {
		s.logger.Error("获取告警规则列表失败",
			zap.Error(err),
			zap.String("request_id", requestID))
		return &Response{
			Code: 500,
			Data: models.ErrorResponse{
				Error:     "获取告警规则列表失败",
				RequestID: requestID,
			},
		}, err
	}

	// 转换为响应结构
	result := make([]AlertRule, len(rules))
	for i, rule := range rules {
		result[i] = *rule
	}

	return &Response{
		Code: 200,
		Data: map[string]interface{}{
			"rules":      result,
			"total":      len(result),
			"request_id": requestID,
		},
	}, nil
}

// GetAlertRuleByID 根据ID获取告警规则
func (s *Service) GetAlertRuleByID(c *gin.Context) (*Response, error) {
	requestID := middleware.GetRequestID(c)

	id := c.Param("id")
	if id == "" {
		s.logger.Warn("缺少规则ID", zap.String("request_id", requestID))
		return &Response{
			Code: 400,
			Data: models.ErrorResponse{
				Error:     "缺少规则ID",
				RequestID: requestID,
			},
		}, fmt.Errorf("缺少规则ID")
	}

	// 获取规则详情
	rule, err := s.dao.GetAlertRuleByID(id)
	if err != nil {
		s.logger.Error("获取告警规则失败",
			zap.Error(err),
			zap.String("rule_id", id),
			zap.String("request_id", requestID))
		return &Response{
			Code: 500,
			Data: models.ErrorResponse{
				Error:     "获取告警规则失败",
				RequestID: requestID,
			},
		}, err
	}

	if rule == nil {
		s.logger.Warn("告警规则不存在",
			zap.String("rule_id", id),
			zap.String("request_id", requestID))
		return &Response{
			Code: 404,
			Data: models.ErrorResponse{
				Error:     "告警规则不存在",
				RequestID: requestID,
			},
		}, fmt.Errorf("告警规则不存在")
	}

	return &Response{
		Code: 200,
		Data: *rule,
	}, nil
}

// ToggleAlertRule 启用/禁用告警规则
func (s *Service) ToggleAlertRule(c *gin.Context) (*Response, error) {
	requestID := middleware.GetRequestID(c)

	id := c.Param("id")
	if id == "" {
		s.logger.Warn("缺少规则ID", zap.String("request_id", requestID))
		return &Response{
			Code: 400,
			Data: models.ErrorResponse{
				Error:     "缺少规则ID",
				RequestID: requestID,
			},
		}, fmt.Errorf("缺少规则ID")
	}

	// 获取请求体
	var req AlertRuleToggleRequest
	if err := c.BindJSON(&req); err != nil {
		s.logger.Warn("解析切换规则状态请求失败",
			zap.Error(err),
			zap.String("request_id", requestID))
		return &Response{
			Code: 400,
			Data: models.ErrorResponse{
				Error:     "无效的请求格式",
				RequestID: requestID,
			},
		}, err
	}

	// 获取规则详情
	rule, err := s.dao.GetAlertRuleByID(id)
	if err != nil {
		s.logger.Error("获取告警规则失败",
			zap.Error(err),
			zap.String("rule_id", id),
			zap.String("request_id", requestID))
		return &Response{
			Code: 500,
			Data: models.ErrorResponse{
				Error:     "获取告警规则失败",
				RequestID: requestID,
			},
		}, err
	}

	if rule == nil {
		s.logger.Warn("告警规则不存在",
			zap.String("rule_id", id),
			zap.String("request_id", requestID))
		return &Response{
			Code: 404,
			Data: models.ErrorResponse{
				Error:     "告警规则不存在",
				RequestID: requestID,
			},
		}, fmt.Errorf("告警规则不存在")
	}

	// 切换状态
	err = s.dao.ToggleAlertRule(id, req.Enabled)
	if err != nil {
		s.logger.Error("切换告警规则状态失败",
			zap.Error(err),
			zap.String("rule_id", id),
			zap.Bool("enabled", req.Enabled),
			zap.String("request_id", requestID))
		return &Response{
			Code: 500,
			Data: models.ErrorResponse{
				Error:     "切换告警规则状态失败",
				RequestID: requestID,
			},
		}, err
	}

	s.logger.Info("告警规则状态已切换",
		zap.String("rule_id", id),
		zap.Bool("enabled", req.Enabled),
		zap.String("request_id", requestID),
	)

	status := "禁用"
	if req.Enabled {
		status = "启用"
	}

	return &Response{
		Code: 200,
		Data: models.SuccessResponse{
			Message:   fmt.Sprintf("告警规则已%s", status),
			RequestID: requestID,
		},
	}, nil
}

// CreateAlertRule 创建告警规则
func (s *Service) CreateAlertRule(c *gin.Context) (*Response, error) {
	requestID, _ := c.Get("requestID")

	// 获取请求体
	var rule AlertRule
	if err := c.BindJSON(&rule); err != nil {
		s.logger.Warn("解析创建规则请求失败",
			zap.Error(err),
			zap.Any("request_id", requestID))
		return &Response{
			Code: 400,
			Data: models.ErrorResponse{
				Error:     "无效的请求格式",
				RequestID: requestID.(string),
			},
		}, err
	}

	// 检查规则名称是否已存在
	existingRule, err := s.dao.GetAlertRuleByName(rule.Name, rule.Source)
	if err != nil {
		s.logger.Error("检查规则名称失败", zap.Error(err), zap.String("name", rule.Name))
		return &Response{
			Code: 500,
			Data: gin.H{"message": "创建告警规则失败"},
		}, err
	}

	if existingRule != nil {
		s.logger.Warn("同名告警规则已存在",
			zap.String("name", rule.Name),
			zap.Any("request_id", requestID))
		return &Response{
			Code: 409,
			Data: models.ErrorResponse{
				Error:     "同名告警规则已存在",
				RequestID: requestID.(string),
			},
		}, fmt.Errorf("同名告警规则已存在")
	}

	// 创建规则
	err = s.dao.CreateAlertRule(&rule)
	if err != nil {
		s.logger.Error("创建告警规则失败", zap.Error(err), zap.String("name", rule.Name))
		return &Response{
			Code: 500,
			Data: gin.H{"message": "创建告警规则失败"},
		}, err
	}

	s.logger.Info("创建告警规则成功",
		zap.String("rule_id", rule.ID),
		zap.String("name", rule.Name),
		zap.Any("request_id", requestID))

	return &Response{
		Code: 200,
		Data: rule,
	}, nil
}

// UpdateAlertRule 更新告警规则
func (s *Service) UpdateAlertRule(c *gin.Context) (*Response, error) {
	requestID, _ := c.Get("requestID")

	id := c.Param("id")
	if id == "" {
		s.logger.Warn("缺少规则ID", zap.Any("request_id", requestID))
		return &Response{
			Code: 400,
			Data: models.ErrorResponse{
				Error:     "缺少规则ID",
				RequestID: requestID.(string),
			},
		}, fmt.Errorf("缺少规则ID")
	}

	// 获取请求体
	var rule AlertRule
	if err := c.BindJSON(&rule); err != nil {
		s.logger.Warn("解析更新规则请求失败",
			zap.Error(err),
			zap.Any("request_id", requestID))
		return &Response{
			Code: 400,
			Data: models.ErrorResponse{
				Error:     "无效的请求格式",
				RequestID: requestID.(string),
			},
		}, err
	}

	// 确保ID一致
	rule.ID = id

	// 获取现有规则
	existingRule, err := s.dao.GetAlertRuleByID(id)
	if err != nil {
		s.logger.Error("获取告警规则失败", zap.Error(err), zap.String("rule_id", id))
		return &Response{
			Code: 500,
			Data: gin.H{"message": "获取告警规则失败"},
		}, err
	}

	if existingRule == nil {
		return &Response{
			Code: 404,
			Data: gin.H{"message": "告警规则不存在"},
		}, fmt.Errorf("告警规则不存在")
	}

	// 检查是否存在同名规则（排除自身）
	if rule.Name != existingRule.Name || rule.Source != existingRule.Source {
		sameNameRule, err := s.dao.GetAlertRuleByName(rule.Name, rule.Source)
		if err != nil {
			s.logger.Error("检查规则名称失败", zap.Error(err), zap.String("name", rule.Name))
			return &Response{
				Code: 500,
				Data: gin.H{"message": "更新告警规则失败"},
			}, err
		}

		if sameNameRule != nil && sameNameRule.ID != id {
			return &Response{
				Code: 409,
				Data: gin.H{"message": "同名告警规则已存在"},
			}, fmt.Errorf("同名告警规则已存在")
		}
	}

	// 保留创建时间
	rule.CreatedAt = existingRule.CreatedAt

	// 更新规则
	err = s.dao.UpdateAlertRule(&rule)
	if err != nil {
		s.logger.Error("更新告警规则失败", zap.Error(err), zap.String("rule_id", id))
		return &Response{
			Code: 500,
			Data: gin.H{"message": "更新告警规则失败"},
		}, err
	}

	s.logger.Info("更新告警规则成功",
		zap.String("rule_id", id),
		zap.Any("request_id", requestID))

	return &Response{
		Code: 200,
		Data: rule,
	}, nil
}

// DeleteAlertRule 删除告警规则
func (s *Service) DeleteAlertRule(c *gin.Context) (*Response, error) {
	requestID, _ := c.Get("requestID")

	id := c.Param("id")
	if id == "" {
		s.logger.Warn("缺少规则ID", zap.Any("request_id", requestID))
		return &Response{
			Code: 400,
			Data: models.ErrorResponse{
				Error:     "缺少规则ID",
				RequestID: requestID.(string),
			},
		}, fmt.Errorf("缺少规则ID")
	}

	// 获取现有规则
	rule, err := s.dao.GetAlertRuleByID(id)
	if err != nil {
		s.logger.Error("获取告警规则失败", zap.Error(err), zap.String("rule_id", id))
		return &Response{
			Code: 500,
			Data: gin.H{"message": "获取告警规则失败"},
		}, err
	}

	if rule == nil {
		return &Response{
			Code: 404,
			Data: gin.H{"message": "告警规则不存在"},
		}, fmt.Errorf("告警规则不存在")
	}

	// 删除规则
	err = s.dao.DeleteAlertRule(id)
	if err != nil {
		s.logger.Error("删除告警规则失败", zap.Error(err), zap.String("rule_id", id))
		return &Response{
			Code: 500,
			Data: gin.H{"message": "删除告警规则失败"},
		}, err
	}

	s.logger.Info("删除告警规则成功",
		zap.String("rule_id", id),
		zap.String("name", rule.Name),
		zap.Any("request_id", requestID))

	return &Response{
		Code: 200,
		Data: models.SuccessResponse{
			Message:   "告警规则已删除",
			RequestID: requestID.(string),
		},
	}, nil
}
