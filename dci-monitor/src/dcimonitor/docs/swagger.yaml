basePath: /
definitions:
  alert.Alert:
    properties:
      acknowledged_at:
        type: string
      acknowledged_by:
        type: string
      annotations:
        $ref: '#/definitions/alert.JSONMap'
      description:
        type: string
      ends_at:
        type: string
      generator_url:
        type: string
      id:
        type: string
      labels:
        $ref: '#/definitions/alert.JSONMap'
      level:
        type: string
      name:
        type: string
      note:
        type: string
      notification_due_at:
        type: string
      resolved_at:
        type: string
      rule_id:
        type: string
      source:
        type: string
      starts_at:
        type: string
      status:
        type: string
      updated_at:
        type: string
    type: object
  alert.AlertAcknowledgeRequest:
    properties:
      acknowledged_by:
        description: 告警确认人，必填字段，表示谁在处理此告警
        example: 张三
        type: string
      note:
        description: 处理备注，选填，可以添加处理进展或计划
        example: 正在联系网络组排查原因
        type: string
    required:
    - acknowledged_by
    type: object
  alert.AlertAcknowledgeResponse:
    properties:
      code:
        description: 状态码
        example: 200
        type: integer
      message:
        description: 响应消息
        example: 告警已确认
        type: string
    type: object
  alert.AlertListResponse:
    properties:
      alerts:
        description: 告警列表
        items:
          $ref: '#/definitions/alert.Alert'
        type: array
      page:
        description: 当前页码
        type: integer
      page_count:
        description: 总页数
        type: integer
      page_size:
        description: 每页数量
        type: integer
      total:
        description: 总记录数
        type: integer
    type: object
  alert.AlertRule:
    properties:
      annotations:
        $ref: '#/definitions/alert.JSONMap'
      created_at:
        type: string
      description:
        type: string
      duration:
        type: integer
      enabled:
        type: boolean
      expression:
        type: string
      id:
        type: string
      labels:
        $ref: '#/definitions/alert.JSONMap'
      level:
        type: string
      name:
        type: string
      source:
        type: string
      updated_at:
        type: string
    type: object
  alert.AlertRuleToggleRequest:
    properties:
      enabled:
        description: 是否启用
        type: boolean
    required:
    - enabled
    type: object
  alert.JSONMap:
    additionalProperties:
      type: string
    type: object
  alert.PrometheusWebhookAlert:
    properties:
      annotations:
        additionalProperties:
          type: string
        type: object
      endsAt:
        type: string
      generatorURL:
        type: string
      labels:
        additionalProperties:
          type: string
        type: object
      startsAt:
        type: string
      status:
        type: string
    type: object
  alert.PrometheusWebhookRequest:
    properties:
      alerts:
        items:
          $ref: '#/definitions/alert.PrometheusWebhookAlert'
        type: array
      commonAnnotations:
        additionalProperties:
          type: string
        type: object
      commonLabels:
        additionalProperties:
          type: string
        type: object
      externalURL:
        type: string
      groupKey:
        type: string
      groupLabels:
        additionalProperties:
          type: string
        type: object
      receiver:
        type: string
      status:
        type: string
      version:
        type: string
    type: object
  alert.Response:
    properties:
      code:
        description: 状态码
        type: integer
      data:
        description: 响应数据
    type: object
  device.Port:
    properties:
      description:
        type: string
      inErrors:
        type: integer
      inRate:
        description: 上行速率
        type: integer
      name:
        description: 例如 "GigabitEthernet1/0/0"
        type: string
      outErrors:
        type: integer
      outRate:
        description: 下行速率
        type: integer
      physicalState:
        description: '物理状态: "up", "down", "admin_down"'
        type: string
      portId:
        description: Changed field name to PortID
        type: string
      protocolState:
        description: '协议状态: "up", "down"'
        type: string
      rateUnit:
        description: 速率单位 (e.g., "bps")
        type: string
      timestamp:
        description: 流量数据获取时间
        type: string
      totalInBytes:
        description: 上行字节总量
        type: integer
      totalInPkts:
        description: 上行包总量
        type: integer
      totalOutBytes:
        description: 下行字节总量
        type: integer
      totalOutPkts:
        description: 下行包总量
        type: integer
    type: object
  device.PortAdminStatus:
    properties:
      deviceId:
        type: string
      portId:
        type: string
      portName:
        type: string
      status:
        description: 1:up, 2:down
        type: integer
      updateAt:
        type: string
    type: object
  device.PortDownstreamTraffic:
    properties:
      deviceId:
        type: string
      outRate:
        type: integer
      portId:
        type: string
      portName:
        type: string
      rateUnit:
        type: string
      timestamp:
        type: string
      totalOutBytes:
        type: integer
      totalOutPkts:
        type: integer
    type: object
  device.PortStatusResponse:
    properties:
      description:
        type: string
      deviceId:
        type: string
      inErrors:
        type: integer
      inRate:
        description: 上行速率
        type: integer
      name:
        description: 例如 "GigabitEthernet1/0/0"
        type: string
      outErrors:
        type: integer
      outRate:
        description: 下行速率
        type: integer
      physicalState:
        description: '物理状态: "up", "down", "admin_down"'
        type: string
      portId:
        description: Changed field name to PortID
        type: string
      protocolState:
        description: '协议状态: "up", "down"'
        type: string
      rateUnit:
        description: 速率单位 (e.g., "bps")
        type: string
      timestamp:
        description: 流量数据获取时间
        type: string
      totalInBytes:
        description: 上行字节总量
        type: integer
      totalInPkts:
        description: 上行包总量
        type: integer
      totalOutBytes:
        description: 下行字节总量
        type: integer
      totalOutPkts:
        description: 下行包总量
        type: integer
    type: object
  device.PortUpstreamTraffic:
    properties:
      deviceId:
        type: string
      inRate:
        type: integer
      portId:
        type: string
      portName:
        type: string
      rateUnit:
        type: string
      timestamp:
        type: string
      totalInBytes:
        type: integer
      totalInPkts:
        type: integer
    type: object
  device.SwitchStatus:
    properties:
      community:
        type: string
      id:
        type: string
      ip:
        type: string
      lastUpdated:
        type: string
      name:
        type: string
      ports:
        items:
          $ref: '#/definitions/device.Port'
        type: array
      status:
        description: '整体状态: "online", "offline", "unknown"'
        type: string
    type: object
  models.ErrorResponse:
    properties:
      details:
        description: 错误详情
        type: string
      error:
        description: 错误信息
        type: string
      request_id:
        description: 请求ID
        type: string
    type: object
  models.Response:
    properties:
      code:
        description: 状态码
        type: integer
      data:
        description: 响应数据
    type: object
  topology.SwaggerTopologyEdge:
    description: 拓扑图中的边（连接）
    properties:
      data:
        description: 连接附加数据，JSON格式
      id:
        description: 边ID
        type: string
      source:
        description: 源节点ID
        type: string
      sourcePort:
        description: 源端口ID
        type: string
      target:
        description: 目标节点ID
        type: string
      targetPort:
        description: 目标端口ID
        type: string
      type:
        description: 连接类型：physical、logical等
        type: string
    type: object
  topology.SwaggerTopologyGraph:
    description: 完整的拓扑图
    properties:
      createdAt:
        description: 创建时间
        type: string
      edges:
        description: 边列表
        items:
          $ref: '#/definitions/topology.SwaggerTopologyEdge'
        type: array
      id:
        description: 拓扑图ID
        type: string
      name:
        description: 拓扑图名称
        type: string
      nodes:
        description: 节点列表
        items:
          $ref: '#/definitions/topology.SwaggerTopologyNode'
        type: array
    type: object
  topology.SwaggerTopologyNode:
    description: 拓扑图中的节点（设备）
    properties:
      data:
        description: 节点附加数据，JSON格式
      id:
        description: 节点ID，与设备ID相同
        type: string
      name:
        description: 节点名称
        type: string
      position:
        description: 节点位置（可选）
      type:
        description: 节点类型：switch、router、server等
        type: string
    type: object
  topology.TopologyStats:
    properties:
      edgeCount:
        description: 边数量
        type: integer
      logicalLinks:
        description: 逻辑链接数量
        type: integer
      nodeCount:
        description: 节点数量
        type: integer
      physicalLinks:
        description: 物理链接数量
        type: integer
      routerCount:
        description: 路由器数量
        type: integer
      serverCount:
        description: 服务器数量
        type: integer
      switchCount:
        description: 交换机数量
        type: integer
    type: object
  traffic.PortFlowData:
    properties:
      in_rate:
        type: number
      in_total:
        type: number
      out_rate:
        type: number
      out_total:
        type: number
      unit_rate:
        description: 速率单位, e.g., "Mbps"
        type: string
      unit_total:
        description: 总量单位, e.g., "MB", "GB"
        type: string
    type: object
  traffic.PortFlowQueryDetails:
    properties:
      device_id:
        type: string
      end_time:
        type: string
      port_id:
        type: string
      start_time:
        type: string
      vni:
        type: string
    type: object
  traffic.PortFlowResponse:
    properties:
      flow_data:
        $ref: '#/definitions/traffic.PortFlowData'
      query_details:
        $ref: '#/definitions/traffic.PortFlowQueryDetails'
      request_id:
        type: string
    type: object
  traffic.PortHistoryData:
    properties:
      series:
        description: 数据系列数组
        items:
          $ref: '#/definitions/traffic.TrafficSeries'
        type: array
      timestamps:
        description: RFC3339 UTC 格式的时间戳数组
        items:
          type: string
        type: array
      unit:
        description: 数据单位 (e.g., "Mbps")
        type: string
    type: object
  traffic.PortHistoryQueryDetails:
    properties:
      device_id:
        type: string
      end_time:
        type: string
      port_id:
        type: string
      start_time:
        type: string
      step:
        type: string
      vni:
        type: string
    type: object
  traffic.PortHistoryResponse:
    properties:
      history_data:
        $ref: '#/definitions/traffic.PortHistoryData'
      query_details:
        $ref: '#/definitions/traffic.PortHistoryQueryDetails'
      request_id:
        type: string
    type: object
  traffic.TrafficChartData:
    properties:
      series:
        description: 数据系列数组
        items:
          $ref: '#/definitions/traffic.TrafficSeries'
        type: array
      timestamps:
        description: RFC3339 UTC 格式的时间戳数组
        items:
          type: string
        type: array
      unit:
        description: 数据单位 (e.g., "Mbps", "Kbps")
        type: string
    type: object
  traffic.TrafficSeries:
    properties:
      data:
        description: 与 Timestamps 对应的数值数组
        items:
          type: number
        type: array
      name:
        description: 系列名称 (e.g., "A端 (CE1-GE1/0/1) 平均入流量")
        type: string
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: DCI监控团队
  description: DCI监控系统提供的告警、设备状态、流量监测等API接口
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: DCI监控系统API
  version: "1.0"
paths:
  /api/v1/alert-rules:
    get:
      description: 获取系统中配置的告警规则列表
      parameters:
      - description: 规则来源
        in: query
        name: source
        type: string
      - description: 是否启用
        in: query
        name: enabled
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: 规则列表
          schema:
            $ref: '#/definitions/alert.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/alert.Response'
      summary: 获取告警规则列表
      tags:
      - 告警规则
    post:
      consumes:
      - application/json
      description: 创建新的告警规则
      parameters:
      - description: 规则定义
        in: body
        name: rule
        required: true
        schema:
          $ref: '#/definitions/alert.AlertRule'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/alert.Response'
            - properties:
                data:
                  $ref: '#/definitions/alert.AlertRule'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/alert.Response'
        "409":
          description: 同名规则已存在
          schema:
            $ref: '#/definitions/alert.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/alert.Response'
      summary: 创建告警规则
      tags:
      - 告警规则
  /api/v1/alert-rules/{id}:
    delete:
      description: 删除现有告警规则
      parameters:
      - description: 规则ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/alert.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/alert.Response'
        "404":
          description: 规则不存在
          schema:
            $ref: '#/definitions/alert.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/alert.Response'
      summary: 删除告警规则
      tags:
      - 告警规则
    get:
      description: 根据规则ID获取告警规则详细信息
      parameters:
      - description: 规则ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 规则详情
          schema:
            allOf:
            - $ref: '#/definitions/alert.Response'
            - properties:
                data:
                  $ref: '#/definitions/alert.AlertRule'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/alert.Response'
        "404":
          description: 规则不存在
          schema:
            $ref: '#/definitions/alert.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/alert.Response'
      summary: 获取告警规则详情
      tags:
      - 告警规则
    put:
      consumes:
      - application/json
      description: 更新现有告警规则
      parameters:
      - description: 规则ID
        in: path
        name: id
        required: true
        type: string
      - description: 规则定义
        in: body
        name: rule
        required: true
        schema:
          $ref: '#/definitions/alert.AlertRule'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            allOf:
            - $ref: '#/definitions/alert.Response'
            - properties:
                data:
                  $ref: '#/definitions/alert.AlertRule'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/alert.Response'
        "404":
          description: 规则不存在
          schema:
            $ref: '#/definitions/alert.Response'
        "409":
          description: 同名规则已存在
          schema:
            $ref: '#/definitions/alert.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/alert.Response'
      summary: 更新告警规则
      tags:
      - 告警规则
  /api/v1/alert-rules/{id}/toggle:
    put:
      consumes:
      - application/json
      description: 切换告警规则的启用状态
      parameters:
      - description: 规则ID
        in: path
        name: id
        required: true
        type: string
      - description: 状态设置
        in: body
        name: toggle
        required: true
        schema:
          $ref: '#/definitions/alert.AlertRuleToggleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 操作成功
          schema:
            $ref: '#/definitions/alert.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/alert.Response'
        "404":
          description: 规则不存在
          schema:
            $ref: '#/definitions/alert.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/alert.Response'
      summary: 启用/禁用告警规则
      tags:
      - 告警规则
  /api/v1/alerts:
    get:
      description: 获取符合筛选条件的告警列表，支持分页
      parameters:
      - description: 告警来源
        in: query
        name: source
        type: string
      - description: 告警级别(critical/warning/info)
        in: query
        name: level
        type: string
      - description: 告警状态(firing/acknowledged/resolved)
        in: query
        name: status
        type: string
      - description: 设备ID
        in: query
        name: device_id
        type: string
      - description: 页码(默认1)
        in: query
        name: page
        type: integer
      - description: 每页数量(默认20，最大100)
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回
          schema:
            allOf:
            - $ref: '#/definitions/alert.Response'
            - properties:
                data:
                  $ref: '#/definitions/alert.AlertListResponse'
              type: object
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/alert.Response'
      summary: 获取告警列表
      tags:
      - 告警管理
  /api/v1/alerts/{id}:
    get:
      description: 根据告警ID获取详细信息
      parameters:
      - description: 告警ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回
          schema:
            allOf:
            - $ref: '#/definitions/alert.Response'
            - properties:
                data:
                  $ref: '#/definitions/alert.Alert'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/alert.Response'
        "404":
          description: 告警不存在
          schema:
            $ref: '#/definitions/alert.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/alert.Response'
      summary: 获取告警详情
      tags:
      - 告警管理
  /api/v1/alerts/{id}/acknowledge:
    put:
      consumes:
      - application/json
      description: 标记告警为已确认状态，表明有人正在处理该告警
      parameters:
      - description: 告警ID
        example: '"a1b2c3d4-e5f6-7890-abcd-ef1234567890"'
        in: path
        name: id
        required: true
        type: string
      - description: 告警确认请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/alert.AlertAcknowledgeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 告警确认成功" example({"code":200,"message":"告警已确认"})
          schema:
            $ref: '#/definitions/alert.AlertAcknowledgeResponse'
        "400":
          description: 请求参数错误" example({"code":400,"data":{"message":"请求体不能为空"}})
          schema:
            $ref: '#/definitions/models.Response'
        "404":
          description: 告警不存在" example({"code":404,"data":{"message":"告警不存在"}})
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: '服务器内部错误" example({"code":500,"data":{"message":"确认告警失败: 数据库错误"}})'
          schema:
            $ref: '#/definitions/models.Response'
      summary: 确认告警
      tags:
      - 告警管理
  /api/v1/alerts/statistics:
    get:
      description: 获取告警的各种统计数据，包括按状态、级别和时间的统计
      produces:
      - application/json
      responses:
        "200":
          description: 统计数据
          schema:
            $ref: '#/definitions/alert.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/alert.Response'
      summary: 获取告警统计
      tags:
      - 告警管理
  /api/v1/alerts/webhook/prometheus:
    post:
      consumes:
      - application/json
      description: 接收并处理Prometheus AlertManager发送的告警通知
      parameters:
      - description: Prometheus告警数据
        in: body
        name: webhook
        required: true
        schema:
          $ref: '#/definitions/alert.PrometheusWebhookRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 成功处理
          schema:
            $ref: '#/definitions/alert.Response'
        "400":
          description: 请求格式错误
          schema:
            $ref: '#/definitions/alert.Response'
      summary: 处理Prometheus告警Webhook
      tags:
      - 告警管理
  /api/v1/switches/{deviceID}:
    get:
      consumes:
      - application/json
      description: 根据提供的设备 ID，获取该交换机的当前详细状态信息，包括 CPU 利用率、内存利用率以及所有端口的基本状态列表。
      parameters:
      - description: 要查询状态的设备 ID
        example: CE1
        in: path
        name: deviceID
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功获取交换机状态
          schema:
            $ref: '#/definitions/device.SwitchStatus'
        "404":
          description: 交换机未找到
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 获取交换机完整状态
      tags:
      - 05-设备监控
  /api/v1/switches/{deviceID}/ports/{ifName}:
    get:
      consumes:
      - application/json
      description: |-
        根据路径参数 `deviceID`, `ifName` 和查询参数 `data` 获取特定端口的信息。
        注意: `ifName` 参数使用Base64编码，是为了安全地在URL路径中传递包含特殊字符（如'/'）的接口名称。
        例如，接口 "10GE1/0/1" 应当编码为 "MTBHRS8wLzE="。
      parameters:
      - description: 端口所属的设备 ID
        example: "210"
        in: path
        name: deviceID
        required: true
        type: string
      - description: 要查询的端口名称，使用Base64编码。例如，将 '10GE1/0/1' 编码为 'MTBHRS8wLzE='
        example: '{{''10GE1/0/1''|base64}}'
        in: path
        name: ifName
        required: true
        type: string
      - default: status
        description: 请求的数据类型。如果省略或提供无效值, 默认为 status。
        enum:
        - status
        - upstream
        - downstream
        in: query
        name: data
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 当 data=downstream 时的响应，包含下行流量数据：totalOutBytes, totalOutPkts,
            outUtil等
          schema:
            $ref: '#/definitions/device.PortDownstreamTraffic'
        "400":
          description: 无效的请求参数
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "404":
          description: 指定的设备或端口未找到
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 获取端口数据 (状态/上行/下行)
      tags:
      - 05-设备监控
  /api/v1/switches/{deviceID}/ports/{ifName}/admin:
    get:
      consumes:
      - application/json
      description: |-
        根据路径参数 `deviceID` 和 `ifName` 获取特定端口的管理状态信息。
        注意: ifName 使用 Base64 编码，不再支持特殊字符的 URL 编码方式。
        例如："100GE1/0/6" 编码为 "MTAwR0UxLzAvNg=="
      parameters:
      - description: 端口所属的设备 ID
        example: "210"
        in: path
        name: deviceID
        required: true
        type: string
      - description: 要查询的端口名称，使用Base64编码
        example: MTAwR0UxLzAvNg==
        in: path
        name: ifName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功获取端口管理状态
          schema:
            $ref: '#/definitions/device.PortAdminStatus'
        "400":
          description: 无效的请求参数
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "404":
          description: 指定的设备或端口未找到
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 获取端口管理状态
      tags:
      - 05-设备监控
  /api/v1/switches/{deviceID}/ports/{ifName}/flow:
    get:
      consumes:
      - application/json
      description: |-
        查询单个端口在指定时间范围内的平均速率和总流量。
        时间范围通过 `start_time` 和 `end_time` 指定，格式为 RFC3339 UTC。如果未提供，则默认为最近15分钟。
        注意: ifName 使用 Base64 编码，不再支持特殊字符的 URL 编码方式。
      parameters:
      - description: 设备ID
        example: "210"
        in: path
        name: deviceID
        required: true
        type: string
      - description: 端口名称，使用Base64编码
        example: MTAwR0UxLzAvNg==
        in: path
        name: ifName
        required: true
        type: string
      - description: VNI (Virtual Network Identifier) 标识符，用于进一步筛选流量
        example: "6005002"
        in: query
        name: vni
        type: string
      - description: 查询起始时间 (RFC3339 UTC 格式)。默认为15分钟前
        example: "2025-06-04T10:00:00Z"
        in: query
        name: start_time
        type: string
      - description: 查询结束时间 (RFC3339 UTC 格式)。默认为当前时间
        example: "2025-06-06T10:15:00Z"
        in: query
        name: end_time
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回端口流量数据
          schema:
            $ref: '#/definitions/traffic.PortFlowResponse'
        "400":
          description: 请求参数无效
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 查询单端口实时流量
      tags:
      - 07-性能管理-单端口流量查询
  /api/v1/switches/{deviceID}/ports/{ifName}/flow/history:
    get:
      consumes:
      - application/json
      description: |-
        查询单个端口在指定时间范围内的历史流量速率时间序列。
        时间范围通过 `start_time` 和 `end_time` 指定，格式为 RFC3339 UTC。
        `step` 参数定义了数据点之间的时间间隔。
        注意: ifName 使用 Base64 编码，不再支持特殊字符的 URL 编码方式。
      parameters:
      - description: 设备ID
        example: "210"
        in: path
        name: deviceID
        required: true
        type: string
      - description: 端口名称，使用Base64编码
        example: '{{''10GE1/0/1''|base64}}'
        in: path
        name: ifName
        required: true
        type: string
      - description: VNI (Virtual Network Identifier) 标识符，用于进一步筛选流量
        example: "6005002"
        in: query
        name: vni
        type: string
      - description: 查询起始时间 (RFC3339 UTC 格式)
        example: "2025-06-18T15:00:00Z"
        in: query
        name: start_time
        required: true
        type: string
      - description: 查询结束时间 (RFC3339 UTC 格式)
        example: "2025-06-18T16:15:00Z"
        in: query
        name: end_time
        required: true
        type: string
      - description: 查询步长, 格式为 Prometheus 的 duration string (例如, '1m', '5m', '1h')。默认为'1m'
        example: 5m
        in: query
        name: step
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回端口历史流量数据
          schema:
            $ref: '#/definitions/traffic.PortHistoryResponse'
        "400":
          description: 请求参数无效
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 查询单端口历史流量
      tags:
      - 07-性能管理-单端口流量查询
  /api/v1/topology:
    get:
      consumes:
      - application/json
      description: 获取当前网络拓扑图，包括所有设备节点和连接
      produces:
      - application/json
      responses:
        "200":
          description: 成功获取拓扑图
          schema:
            $ref: '#/definitions/topology.SwaggerTopologyGraph'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 获取当前拓扑图
      tags:
      - 拓扑管理
  /api/v1/topology/health:
    get:
      consumes:
      - application/json
      description: 获取拓扑服务的健康状态，包括数据库连接、Kafka连接等
      produces:
      - application/json
      responses:
        "200":
          description: 成功获取健康状态
          schema:
            additionalProperties: true
            type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 获取拓扑服务健康状态
      tags:
      - 拓扑管理
  /api/v1/topology/stats:
    get:
      consumes:
      - application/json
      description: 获取当前拓扑图的统计信息，如节点数量和连接数量
      produces:
      - application/json
      responses:
        "200":
          description: 成功获取拓扑统计信息
          schema:
            $ref: '#/definitions/topology.TopologyStats'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 获取拓扑统计信息
      tags:
      - 拓扑管理
  /api/v1/traffic/chart/average:
    get:
      consumes:
      - application/json
      description: |-
        查询A-Z链路在指定时间范围内的平均流量速率时间序列。支持通过VNI进行筛选。
        **注意: `a_port_id` 和 `z_port_id` 中的特殊字符 (如 '/') 需要进行 URL 编码。**
      parameters:
      - description: A端设备ID
        example: CE1
        in: query
        name: a_switch_id
        required: true
        type: string
      - description: A端端口ID (URL Encoded)
        example: GE1%2F0%2F1
        in: query
        name: a_port_id
        required: true
        type: string
      - description: Z端设备ID
        example: CE2
        in: query
        name: z_switch_id
        required: true
        type: string
      - description: Z端端口ID (URL Encoded)
        example: GE1%2F0%2F1
        in: query
        name: z_port_id
        required: true
        type: string
      - description: 查询步长或数据聚合粒度, 格式为 Prometheus 的 duration string (例如, '1m', '5m',
          '1h')。
        example: 1m
        in: query
        name: granularity
        required: true
        type: string
      - description: VNI (Virtual Network Identifier) 标识符
        example: "6005002"
        in: query
        name: vni
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回平均流量数据
          schema:
            $ref: '#/definitions/traffic.TrafficChartData'
        "400":
          description: 请求参数无效
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 获取A-Z链路平均流量图
      tags:
      - 06-性能管理-A-Z链路流量
  /api/v1/traffic/chart/maximum:
    get:
      consumes:
      - application/json
      description: |-
        查询A-Z链路在指定时间范围内的峰值流量速率时间序列。支持通过VNI进行筛选。
        **注意: `a_port_id` 和 `z_port_id` 中的特殊字符 (如 '/') 需要进行 URL 编码。**
      parameters:
      - description: A端设备ID
        example: CE1
        in: query
        name: a_switch_id
        required: true
        type: string
      - description: A端端口ID (URL Encoded)
        example: GE1%2F0%2F1
        in: query
        name: a_port_id
        required: true
        type: string
      - description: Z端设备ID
        example: CE2
        in: query
        name: z_switch_id
        required: true
        type: string
      - description: Z端端口ID (URL Encoded)
        example: GE1%2F0%2F1
        in: query
        name: z_port_id
        required: true
        type: string
      - description: 查询步长或数据聚合粒度, 格式为 Prometheus 的 duration string (例如, '1m', '5m',
          '1h')。
        example: 1m
        in: query
        name: granularity
        required: true
        type: string
      - description: VNI (Virtual Network Identifier) 标识符
        example: "6005002"
        in: query
        name: vni
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回峰值流量数据
          schema:
            $ref: '#/definitions/traffic.TrafficChartData'
        "400":
          description: 请求参数无效
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 获取A-Z链路最大(峰值)流量图
      tags:
      - 06-性能管理-A-Z链路流量
  /api/v1/traffic/chart/minimum:
    get:
      consumes:
      - application/json
      description: |-
        查询A-Z链路在指定时间范围内的谷值流量速率时间序列。支持通过VNI进行筛选。
        **注意: `a_port_id` 和 `z_port_id` 中的特殊字符 (如 '/') 需要进行 URL 编码。**
      parameters:
      - description: A端设备ID
        example: CE1
        in: query
        name: a_switch_id
        required: true
        type: string
      - description: A端端口ID (URL Encoded)
        example: GE1%2F0%2F1
        in: query
        name: a_port_id
        required: true
        type: string
      - description: Z端设备ID
        example: CE2
        in: query
        name: z_switch_id
        required: true
        type: string
      - description: Z端端口ID (URL Encoded)
        example: GE1%2F0%2F1
        in: query
        name: z_port_id
        required: true
        type: string
      - description: 查询步长或数据聚合粒度, 格式为 Prometheus 的 duration string (例如, '1m', '5m',
          '1h')。
        example: 1m
        in: query
        name: granularity
        required: true
        type: string
      - description: VNI (Virtual Network Identifier) 标识符
        example: "6005002"
        in: query
        name: vni
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回谷值流量数据
          schema:
            $ref: '#/definitions/traffic.TrafficChartData'
        "400":
          description: 请求参数无效
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 获取A-Z链路最小(谷值)流量图
      tags:
      - 06-性能管理-A-Z链路流量
  /api/v1/traffic/summary:
    get:
      consumes:
      - application/json
      description: |-
        获取A-Z链路在指定时间范围内的流量摘要信息，如总流量、平均/峰值/谷值速率等。 **注意：此接口当前尚未实现。**
        **注意: `a_port_id` 和 `z_port_id` 中的特殊字符 (如 '/') 需要进行 URL 编码。**
      parameters:
      - description: A端设备ID
        example: CE1
        in: query
        name: a_switch_id
        required: true
        type: string
      - description: A端端口ID (URL Encoded)
        example: GE1%2F0%2F1
        in: query
        name: a_port_id
        required: true
        type: string
      - description: Z端设备ID
        example: CE2
        in: query
        name: z_switch_id
        required: true
        type: string
      - description: Z端端口ID (URL Encoded)
        example: GE1%2F0%2F1
        in: query
        name: z_port_id
        required: true
        type: string
      - description: 查询步长或数据聚合粒度, 格式为 Prometheus 的 duration string (例如, '1m', '5m',
          '1h')。
        example: 1m
        in: query
        name: granularity
        required: true
        type: string
      - description: VNI (Virtual Network Identifier) 标识符
        example: "6005002"
        in: query
        name: vni
        type: string
      produces:
      - application/json
      responses:
        "501":
          description: 接口未实现
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: 获取A-Z链路流量摘要
      tags:
      - 06-性能管理-A-Z链路流量
schemes:
- http
- https
swagger: "2.0"
