// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "DCI监控团队",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/v1/alert-rules": {
            "get": {
                "description": "获取系统中配置的告警规则列表",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警规则"
                ],
                "summary": "获取告警规则列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "规则来源",
                        "name": "source",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "是否启用",
                        "name": "enabled",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "规则列表",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    }
                }
            },
            "post": {
                "description": "创建新的告警规则",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警规则"
                ],
                "summary": "创建告警规则",
                "parameters": [
                    {
                        "description": "规则定义",
                        "name": "rule",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/alert.AlertRule"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/alert.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/alert.AlertRule"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    },
                    "409": {
                        "description": "同名规则已存在",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/alert-rules/{id}": {
            "get": {
                "description": "根据规则ID获取告警规则详细信息",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警规则"
                ],
                "summary": "获取告警规则详情",
                "parameters": [
                    {
                        "type": "string",
                        "description": "规则ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "规则详情",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/alert.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/alert.AlertRule"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    },
                    "404": {
                        "description": "规则不存在",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    }
                }
            },
            "put": {
                "description": "更新现有告警规则",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警规则"
                ],
                "summary": "更新告警规则",
                "parameters": [
                    {
                        "type": "string",
                        "description": "规则ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "规则定义",
                        "name": "rule",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/alert.AlertRule"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/alert.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/alert.AlertRule"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    },
                    "404": {
                        "description": "规则不存在",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    },
                    "409": {
                        "description": "同名规则已存在",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    }
                }
            },
            "delete": {
                "description": "删除现有告警规则",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警规则"
                ],
                "summary": "删除告警规则",
                "parameters": [
                    {
                        "type": "string",
                        "description": "规则ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    },
                    "404": {
                        "description": "规则不存在",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/alert-rules/{id}/toggle": {
            "put": {
                "description": "切换告警规则的启用状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警规则"
                ],
                "summary": "启用/禁用告警规则",
                "parameters": [
                    {
                        "type": "string",
                        "description": "规则ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "状态设置",
                        "name": "toggle",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/alert.AlertRuleToggleRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "操作成功",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    },
                    "404": {
                        "description": "规则不存在",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/alerts": {
            "get": {
                "description": "获取符合筛选条件的告警列表，支持分页",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "获取告警列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "告警来源",
                        "name": "source",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "告警级别(critical/warning/info)",
                        "name": "level",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "告警状态(firing/acknowledged/resolved)",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "设备ID",
                        "name": "device_id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码(默认1)",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页数量(默认20，最大100)",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/alert.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/alert.AlertListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/alerts/statistics": {
            "get": {
                "description": "获取告警的各种统计数据，包括按状态、级别和时间的统计",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "获取告警统计",
                "responses": {
                    "200": {
                        "description": "统计数据",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/alerts/webhook/prometheus": {
            "post": {
                "description": "接收并处理Prometheus AlertManager发送的告警通知",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "处理Prometheus告警Webhook",
                "parameters": [
                    {
                        "description": "Prometheus告警数据",
                        "name": "webhook",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/alert.PrometheusWebhookRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功处理",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    },
                    "400": {
                        "description": "请求格式错误",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/alerts/{id}": {
            "get": {
                "description": "根据告警ID获取详细信息",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "获取告警详情",
                "parameters": [
                    {
                        "type": "string",
                        "description": "告警ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/alert.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/alert.Alert"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    },
                    "404": {
                        "description": "告警不存在",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/alert.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/alerts/{id}/acknowledge": {
            "put": {
                "description": "标记告警为已确认状态，表明有人正在处理该告警",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "确认告警",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"a1b2c3d4-e5f6-7890-abcd-ef1234567890\"",
                        "description": "告警ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "告警确认请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/alert.AlertAcknowledgeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "告警确认成功\" example({\"code\":200,\"message\":\"告警已确认\"})",
                        "schema": {
                            "$ref": "#/definitions/alert.AlertAcknowledgeResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误\" example({\"code\":400,\"data\":{\"message\":\"请求体不能为空\"}})",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "404": {
                        "description": "告警不存在\" example({\"code\":404,\"data\":{\"message\":\"告警不存在\"}})",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误\" example({\"code\":500,\"data\":{\"message\":\"确认告警失败: 数据库错误\"}})",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/switches/{deviceID}": {
            "get": {
                "description": "根据提供的设备 ID，获取该交换机的当前详细状态信息，包括 CPU 利用率、内存利用率以及所有端口的基本状态列表。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "05-设备监控"
                ],
                "summary": "获取交换机完整状态",
                "parameters": [
                    {
                        "type": "string",
                        "example": "CE1",
                        "description": "要查询状态的设备 ID",
                        "name": "deviceID",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功获取交换机状态",
                        "schema": {
                            "$ref": "#/definitions/device.SwitchStatus"
                        }
                    },
                    "404": {
                        "description": "交换机未找到",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/switches/{deviceID}/ports/{ifName}": {
            "get": {
                "description": "根据路径参数 ` + "`" + `deviceID` + "`" + `, ` + "`" + `ifName` + "`" + ` 和查询参数 ` + "`" + `data` + "`" + ` 获取特定端口的信息。\n注意: ` + "`" + `ifName` + "`" + ` 参数使用Base64编码，是为了安全地在URL路径中传递包含特殊字符（如'/'）的接口名称。\n例如，接口 \"10GE1/0/1\" 应当编码为 \"MTBHRS8wLzE=\"。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "05-设备监控"
                ],
                "summary": "获取端口数据 (状态/上行/下行)",
                "parameters": [
                    {
                        "type": "string",
                        "example": "210",
                        "description": "端口所属的设备 ID",
                        "name": "deviceID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "{{'10GE1/0/1'|base64}}",
                        "description": "要查询的端口名称，使用Base64编码。例如，将 '10GE1/0/1' 编码为 'MTBHRS8wLzE='",
                        "name": "ifName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "enum": [
                            "status",
                            "upstream",
                            "downstream"
                        ],
                        "type": "string",
                        "default": "status",
                        "description": "请求的数据类型。如果省略或提供无效值, 默认为 status。",
                        "name": "data",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "当 data=downstream 时的响应，包含下行流量数据：totalOutBytes, totalOutPkts, outUtil等",
                        "schema": {
                            "$ref": "#/definitions/device.PortDownstreamTraffic"
                        }
                    },
                    "400": {
                        "description": "无效的请求参数",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "指定的设备或端口未找到",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/switches/{deviceID}/ports/{ifName}/admin": {
            "get": {
                "description": "根据路径参数 ` + "`" + `deviceID` + "`" + ` 和 ` + "`" + `ifName` + "`" + ` 获取特定端口的管理状态信息。\n注意: ifName 使用 Base64 编码，不再支持特殊字符的 URL 编码方式。\n例如：\"100GE1/0/6\" 编码为 \"MTAwR0UxLzAvNg==\"",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "05-设备监控"
                ],
                "summary": "获取端口管理状态",
                "parameters": [
                    {
                        "type": "string",
                        "example": "210",
                        "description": "端口所属的设备 ID",
                        "name": "deviceID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "MTAwR0UxLzAvNg==",
                        "description": "要查询的端口名称，使用Base64编码",
                        "name": "ifName",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功获取端口管理状态",
                        "schema": {
                            "$ref": "#/definitions/device.PortAdminStatus"
                        }
                    },
                    "400": {
                        "description": "无效的请求参数",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "指定的设备或端口未找到",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/switches/{deviceID}/ports/{ifName}/flow": {
            "get": {
                "description": "查询单个端口在指定时间范围内的平均速率和总流量。\n时间范围通过 ` + "`" + `start_time` + "`" + ` 和 ` + "`" + `end_time` + "`" + ` 指定，格式为 RFC3339 UTC。如果未提供，则默认为最近15分钟。\n注意: ifName 使用 Base64 编码，不再支持特殊字符的 URL 编码方式。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "07-性能管理-单端口流量查询"
                ],
                "summary": "查询单端口实时流量",
                "parameters": [
                    {
                        "type": "string",
                        "example": "210",
                        "description": "设备ID",
                        "name": "deviceID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "MTAwR0UxLzAvNg==",
                        "description": "端口名称，使用Base64编码",
                        "name": "ifName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "6005002",
                        "description": "VNI (Virtual Network Identifier) 标识符，用于进一步筛选流量",
                        "name": "vni",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "2025-06-04T10:00:00Z",
                        "description": "查询起始时间 (RFC3339 UTC 格式)。默认为15分钟前",
                        "name": "start_time",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "2025-06-06T10:15:00Z",
                        "description": "查询结束时间 (RFC3339 UTC 格式)。默认为当前时间",
                        "name": "end_time",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回端口流量数据",
                        "schema": {
                            "$ref": "#/definitions/traffic.PortFlowResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数无效",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/switches/{deviceID}/ports/{ifName}/flow/history": {
            "get": {
                "description": "查询单个端口在指定时间范围内的历史流量速率时间序列。\n时间范围通过 ` + "`" + `start_time` + "`" + ` 和 ` + "`" + `end_time` + "`" + ` 指定，格式为 RFC3339 UTC。\n` + "`" + `step` + "`" + ` 参数定义了数据点之间的时间间隔。\n注意: ifName 使用 Base64 编码，不再支持特殊字符的 URL 编码方式。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "07-性能管理-单端口流量查询"
                ],
                "summary": "查询单端口历史流量",
                "parameters": [
                    {
                        "type": "string",
                        "example": "210",
                        "description": "设备ID",
                        "name": "deviceID",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "{{'10GE1/0/1'|base64}}",
                        "description": "端口名称，使用Base64编码",
                        "name": "ifName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "6005002",
                        "description": "VNI (Virtual Network Identifier) 标识符，用于进一步筛选流量",
                        "name": "vni",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "2025-06-18T15:00:00Z",
                        "description": "查询起始时间 (RFC3339 UTC 格式)",
                        "name": "start_time",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "2025-06-18T16:15:00Z",
                        "description": "查询结束时间 (RFC3339 UTC 格式)",
                        "name": "end_time",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "5m",
                        "description": "查询步长, 格式为 Prometheus 的 duration string (例如, '1m', '5m', '1h')。默认为'1m'",
                        "name": "step",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回端口历史流量数据",
                        "schema": {
                            "$ref": "#/definitions/traffic.PortHistoryResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数无效",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/topology": {
            "get": {
                "description": "获取当前网络拓扑图，包括所有设备节点和连接",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "拓扑管理"
                ],
                "summary": "获取当前拓扑图",
                "responses": {
                    "200": {
                        "description": "成功获取拓扑图",
                        "schema": {
                            "$ref": "#/definitions/topology.SwaggerTopologyGraph"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/topology/health": {
            "get": {
                "description": "获取拓扑服务的健康状态，包括数据库连接、Kafka连接等",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "拓扑管理"
                ],
                "summary": "获取拓扑服务健康状态",
                "responses": {
                    "200": {
                        "description": "成功获取健康状态",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/topology/stats": {
            "get": {
                "description": "获取当前拓扑图的统计信息，如节点数量和连接数量",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "拓扑管理"
                ],
                "summary": "获取拓扑统计信息",
                "responses": {
                    "200": {
                        "description": "成功获取拓扑统计信息",
                        "schema": {
                            "$ref": "#/definitions/topology.TopologyStats"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/traffic/chart/average": {
            "get": {
                "description": "查询A-Z链路在指定时间范围内的平均流量速率时间序列。支持通过VNI进行筛选。\n**注意: ` + "`" + `a_port_id` + "`" + ` 和 ` + "`" + `z_port_id` + "`" + ` 中的特殊字符 (如 '/') 需要进行 URL 编码。**",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "06-性能管理-A-Z链路流量"
                ],
                "summary": "获取A-Z链路平均流量图",
                "parameters": [
                    {
                        "type": "string",
                        "example": "CE1",
                        "description": "A端设备ID",
                        "name": "a_switch_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "GE1%2F0%2F1",
                        "description": "A端端口ID (URL Encoded)",
                        "name": "a_port_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "CE2",
                        "description": "Z端设备ID",
                        "name": "z_switch_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "GE1%2F0%2F1",
                        "description": "Z端端口ID (URL Encoded)",
                        "name": "z_port_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "1m",
                        "description": "查询步长或数据聚合粒度, 格式为 Prometheus 的 duration string (例如, '1m', '5m', '1h')。",
                        "name": "granularity",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "6005002",
                        "description": "VNI (Virtual Network Identifier) 标识符",
                        "name": "vni",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回平均流量数据",
                        "schema": {
                            "$ref": "#/definitions/traffic.TrafficChartData"
                        }
                    },
                    "400": {
                        "description": "请求参数无效",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/traffic/chart/maximum": {
            "get": {
                "description": "查询A-Z链路在指定时间范围内的峰值流量速率时间序列。支持通过VNI进行筛选。\n**注意: ` + "`" + `a_port_id` + "`" + ` 和 ` + "`" + `z_port_id` + "`" + ` 中的特殊字符 (如 '/') 需要进行 URL 编码。**",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "06-性能管理-A-Z链路流量"
                ],
                "summary": "获取A-Z链路最大(峰值)流量图",
                "parameters": [
                    {
                        "type": "string",
                        "example": "CE1",
                        "description": "A端设备ID",
                        "name": "a_switch_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "GE1%2F0%2F1",
                        "description": "A端端口ID (URL Encoded)",
                        "name": "a_port_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "CE2",
                        "description": "Z端设备ID",
                        "name": "z_switch_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "GE1%2F0%2F1",
                        "description": "Z端端口ID (URL Encoded)",
                        "name": "z_port_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "1m",
                        "description": "查询步长或数据聚合粒度, 格式为 Prometheus 的 duration string (例如, '1m', '5m', '1h')。",
                        "name": "granularity",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "6005002",
                        "description": "VNI (Virtual Network Identifier) 标识符",
                        "name": "vni",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回峰值流量数据",
                        "schema": {
                            "$ref": "#/definitions/traffic.TrafficChartData"
                        }
                    },
                    "400": {
                        "description": "请求参数无效",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/traffic/chart/minimum": {
            "get": {
                "description": "查询A-Z链路在指定时间范围内的谷值流量速率时间序列。支持通过VNI进行筛选。\n**注意: ` + "`" + `a_port_id` + "`" + ` 和 ` + "`" + `z_port_id` + "`" + ` 中的特殊字符 (如 '/') 需要进行 URL 编码。**",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "06-性能管理-A-Z链路流量"
                ],
                "summary": "获取A-Z链路最小(谷值)流量图",
                "parameters": [
                    {
                        "type": "string",
                        "example": "CE1",
                        "description": "A端设备ID",
                        "name": "a_switch_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "GE1%2F0%2F1",
                        "description": "A端端口ID (URL Encoded)",
                        "name": "a_port_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "CE2",
                        "description": "Z端设备ID",
                        "name": "z_switch_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "GE1%2F0%2F1",
                        "description": "Z端端口ID (URL Encoded)",
                        "name": "z_port_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "1m",
                        "description": "查询步长或数据聚合粒度, 格式为 Prometheus 的 duration string (例如, '1m', '5m', '1h')。",
                        "name": "granularity",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "6005002",
                        "description": "VNI (Virtual Network Identifier) 标识符",
                        "name": "vni",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回谷值流量数据",
                        "schema": {
                            "$ref": "#/definitions/traffic.TrafficChartData"
                        }
                    },
                    "400": {
                        "description": "请求参数无效",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/traffic/summary": {
            "get": {
                "description": "获取A-Z链路在指定时间范围内的流量摘要信息，如总流量、平均/峰值/谷值速率等。 **注意：此接口当前尚未实现。**\n**注意: ` + "`" + `a_port_id` + "`" + ` 和 ` + "`" + `z_port_id` + "`" + ` 中的特殊字符 (如 '/') 需要进行 URL 编码。**",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "06-性能管理-A-Z链路流量"
                ],
                "summary": "获取A-Z链路流量摘要",
                "parameters": [
                    {
                        "type": "string",
                        "example": "CE1",
                        "description": "A端设备ID",
                        "name": "a_switch_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "GE1%2F0%2F1",
                        "description": "A端端口ID (URL Encoded)",
                        "name": "a_port_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "CE2",
                        "description": "Z端设备ID",
                        "name": "z_switch_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "GE1%2F0%2F1",
                        "description": "Z端端口ID (URL Encoded)",
                        "name": "z_port_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "1m",
                        "description": "查询步长或数据聚合粒度, 格式为 Prometheus 的 duration string (例如, '1m', '5m', '1h')。",
                        "name": "granularity",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "6005002",
                        "description": "VNI (Virtual Network Identifier) 标识符",
                        "name": "vni",
                        "in": "query"
                    }
                ],
                "responses": {
                    "501": {
                        "description": "接口未实现",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "alert.Alert": {
            "type": "object",
            "properties": {
                "acknowledged_at": {
                    "type": "string"
                },
                "acknowledged_by": {
                    "type": "string"
                },
                "annotations": {
                    "$ref": "#/definitions/alert.JSONMap"
                },
                "description": {
                    "type": "string"
                },
                "ends_at": {
                    "type": "string"
                },
                "generator_url": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "labels": {
                    "$ref": "#/definitions/alert.JSONMap"
                },
                "level": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "note": {
                    "type": "string"
                },
                "notification_due_at": {
                    "type": "string"
                },
                "resolved_at": {
                    "type": "string"
                },
                "rule_id": {
                    "type": "string"
                },
                "source": {
                    "type": "string"
                },
                "starts_at": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "alert.AlertAcknowledgeRequest": {
            "type": "object",
            "required": [
                "acknowledged_by"
            ],
            "properties": {
                "acknowledged_by": {
                    "description": "告警确认人，必填字段，表示谁在处理此告警",
                    "type": "string",
                    "example": "张三"
                },
                "note": {
                    "description": "处理备注，选填，可以添加处理进展或计划",
                    "type": "string",
                    "example": "正在联系网络组排查原因"
                }
            }
        },
        "alert.AlertAcknowledgeResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码",
                    "type": "integer",
                    "example": 200
                },
                "message": {
                    "description": "响应消息",
                    "type": "string",
                    "example": "告警已确认"
                }
            }
        },
        "alert.AlertListResponse": {
            "type": "object",
            "properties": {
                "alerts": {
                    "description": "告警列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/alert.Alert"
                    }
                },
                "page": {
                    "description": "当前页码",
                    "type": "integer"
                },
                "page_count": {
                    "description": "总页数",
                    "type": "integer"
                },
                "page_size": {
                    "description": "每页数量",
                    "type": "integer"
                },
                "total": {
                    "description": "总记录数",
                    "type": "integer"
                }
            }
        },
        "alert.AlertRule": {
            "type": "object",
            "properties": {
                "annotations": {
                    "$ref": "#/definitions/alert.JSONMap"
                },
                "created_at": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "duration": {
                    "type": "integer"
                },
                "enabled": {
                    "type": "boolean"
                },
                "expression": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "labels": {
                    "$ref": "#/definitions/alert.JSONMap"
                },
                "level": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "source": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "alert.AlertRuleToggleRequest": {
            "type": "object",
            "required": [
                "enabled"
            ],
            "properties": {
                "enabled": {
                    "description": "是否启用",
                    "type": "boolean"
                }
            }
        },
        "alert.JSONMap": {
            "type": "object",
            "additionalProperties": {
                "type": "string"
            }
        },
        "alert.PrometheusWebhookAlert": {
            "type": "object",
            "properties": {
                "annotations": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                },
                "endsAt": {
                    "type": "string"
                },
                "generatorURL": {
                    "type": "string"
                },
                "labels": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                },
                "startsAt": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "alert.PrometheusWebhookRequest": {
            "type": "object",
            "properties": {
                "alerts": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/alert.PrometheusWebhookAlert"
                    }
                },
                "commonAnnotations": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                },
                "commonLabels": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                },
                "externalURL": {
                    "type": "string"
                },
                "groupKey": {
                    "type": "string"
                },
                "groupLabels": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                },
                "receiver": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "version": {
                    "type": "string"
                }
            }
        },
        "alert.Response": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码",
                    "type": "integer"
                },
                "data": {
                    "description": "响应数据"
                }
            }
        },
        "device.Port": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "inErrors": {
                    "type": "integer"
                },
                "inRate": {
                    "description": "上行速率",
                    "type": "integer"
                },
                "name": {
                    "description": "例如 \"GigabitEthernet1/0/0\"",
                    "type": "string"
                },
                "outErrors": {
                    "type": "integer"
                },
                "outRate": {
                    "description": "下行速率",
                    "type": "integer"
                },
                "physicalState": {
                    "description": "物理状态: \"up\", \"down\", \"admin_down\"",
                    "type": "string"
                },
                "portId": {
                    "description": "Changed field name to PortID",
                    "type": "string"
                },
                "protocolState": {
                    "description": "协议状态: \"up\", \"down\"",
                    "type": "string"
                },
                "rateUnit": {
                    "description": "速率单位 (e.g., \"bps\")",
                    "type": "string"
                },
                "timestamp": {
                    "description": "流量数据获取时间",
                    "type": "string"
                },
                "totalInBytes": {
                    "description": "上行字节总量",
                    "type": "integer"
                },
                "totalInPkts": {
                    "description": "上行包总量",
                    "type": "integer"
                },
                "totalOutBytes": {
                    "description": "下行字节总量",
                    "type": "integer"
                },
                "totalOutPkts": {
                    "description": "下行包总量",
                    "type": "integer"
                }
            }
        },
        "device.PortAdminStatus": {
            "type": "object",
            "properties": {
                "deviceId": {
                    "type": "string"
                },
                "portId": {
                    "type": "string"
                },
                "portName": {
                    "type": "string"
                },
                "status": {
                    "description": "1:up, 2:down",
                    "type": "integer"
                },
                "updateAt": {
                    "type": "string"
                }
            }
        },
        "device.PortDownstreamTraffic": {
            "type": "object",
            "properties": {
                "deviceId": {
                    "type": "string"
                },
                "outRate": {
                    "type": "integer"
                },
                "portId": {
                    "type": "string"
                },
                "portName": {
                    "type": "string"
                },
                "rateUnit": {
                    "type": "string"
                },
                "timestamp": {
                    "type": "string"
                },
                "totalOutBytes": {
                    "type": "integer"
                },
                "totalOutPkts": {
                    "type": "integer"
                }
            }
        },
        "device.PortStatusResponse": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "deviceId": {
                    "type": "string"
                },
                "inErrors": {
                    "type": "integer"
                },
                "inRate": {
                    "description": "上行速率",
                    "type": "integer"
                },
                "name": {
                    "description": "例如 \"GigabitEthernet1/0/0\"",
                    "type": "string"
                },
                "outErrors": {
                    "type": "integer"
                },
                "outRate": {
                    "description": "下行速率",
                    "type": "integer"
                },
                "physicalState": {
                    "description": "物理状态: \"up\", \"down\", \"admin_down\"",
                    "type": "string"
                },
                "portId": {
                    "description": "Changed field name to PortID",
                    "type": "string"
                },
                "protocolState": {
                    "description": "协议状态: \"up\", \"down\"",
                    "type": "string"
                },
                "rateUnit": {
                    "description": "速率单位 (e.g., \"bps\")",
                    "type": "string"
                },
                "timestamp": {
                    "description": "流量数据获取时间",
                    "type": "string"
                },
                "totalInBytes": {
                    "description": "上行字节总量",
                    "type": "integer"
                },
                "totalInPkts": {
                    "description": "上行包总量",
                    "type": "integer"
                },
                "totalOutBytes": {
                    "description": "下行字节总量",
                    "type": "integer"
                },
                "totalOutPkts": {
                    "description": "下行包总量",
                    "type": "integer"
                }
            }
        },
        "device.PortUpstreamTraffic": {
            "type": "object",
            "properties": {
                "deviceId": {
                    "type": "string"
                },
                "inRate": {
                    "type": "integer"
                },
                "portId": {
                    "type": "string"
                },
                "portName": {
                    "type": "string"
                },
                "rateUnit": {
                    "type": "string"
                },
                "timestamp": {
                    "type": "string"
                },
                "totalInBytes": {
                    "type": "integer"
                },
                "totalInPkts": {
                    "type": "integer"
                }
            }
        },
        "device.SwitchStatus": {
            "type": "object",
            "properties": {
                "community": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "ip": {
                    "type": "string"
                },
                "lastUpdated": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "ports": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/device.Port"
                    }
                },
                "status": {
                    "description": "整体状态: \"online\", \"offline\", \"unknown\"",
                    "type": "string"
                }
            }
        },
        "models.ErrorResponse": {
            "type": "object",
            "properties": {
                "details": {
                    "description": "错误详情",
                    "type": "string"
                },
                "error": {
                    "description": "错误信息",
                    "type": "string"
                },
                "request_id": {
                    "description": "请求ID",
                    "type": "string"
                }
            }
        },
        "models.Response": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码",
                    "type": "integer"
                },
                "data": {
                    "description": "响应数据"
                }
            }
        },
        "topology.SwaggerTopologyEdge": {
            "description": "拓扑图中的边（连接）",
            "type": "object",
            "properties": {
                "data": {
                    "description": "连接附加数据，JSON格式"
                },
                "id": {
                    "description": "边ID",
                    "type": "string"
                },
                "source": {
                    "description": "源节点ID",
                    "type": "string"
                },
                "sourcePort": {
                    "description": "源端口ID",
                    "type": "string"
                },
                "target": {
                    "description": "目标节点ID",
                    "type": "string"
                },
                "targetPort": {
                    "description": "目标端口ID",
                    "type": "string"
                },
                "type": {
                    "description": "连接类型：physical、logical等",
                    "type": "string"
                }
            }
        },
        "topology.SwaggerTopologyGraph": {
            "description": "完整的拓扑图",
            "type": "object",
            "properties": {
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "edges": {
                    "description": "边列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/topology.SwaggerTopologyEdge"
                    }
                },
                "id": {
                    "description": "拓扑图ID",
                    "type": "string"
                },
                "name": {
                    "description": "拓扑图名称",
                    "type": "string"
                },
                "nodes": {
                    "description": "节点列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/topology.SwaggerTopologyNode"
                    }
                }
            }
        },
        "topology.SwaggerTopologyNode": {
            "description": "拓扑图中的节点（设备）",
            "type": "object",
            "properties": {
                "data": {
                    "description": "节点附加数据，JSON格式"
                },
                "id": {
                    "description": "节点ID，与设备ID相同",
                    "type": "string"
                },
                "name": {
                    "description": "节点名称",
                    "type": "string"
                },
                "position": {
                    "description": "节点位置（可选）"
                },
                "type": {
                    "description": "节点类型：switch、router、server等",
                    "type": "string"
                }
            }
        },
        "topology.TopologyStats": {
            "type": "object",
            "properties": {
                "edgeCount": {
                    "description": "边数量",
                    "type": "integer"
                },
                "logicalLinks": {
                    "description": "逻辑链接数量",
                    "type": "integer"
                },
                "nodeCount": {
                    "description": "节点数量",
                    "type": "integer"
                },
                "physicalLinks": {
                    "description": "物理链接数量",
                    "type": "integer"
                },
                "routerCount": {
                    "description": "路由器数量",
                    "type": "integer"
                },
                "serverCount": {
                    "description": "服务器数量",
                    "type": "integer"
                },
                "switchCount": {
                    "description": "交换机数量",
                    "type": "integer"
                }
            }
        },
        "traffic.PortFlowData": {
            "type": "object",
            "properties": {
                "in_rate": {
                    "type": "number"
                },
                "in_total": {
                    "type": "number"
                },
                "out_rate": {
                    "type": "number"
                },
                "out_total": {
                    "type": "number"
                },
                "unit_rate": {
                    "description": "速率单位, e.g., \"Mbps\"",
                    "type": "string"
                },
                "unit_total": {
                    "description": "总量单位, e.g., \"MB\", \"GB\"",
                    "type": "string"
                }
            }
        },
        "traffic.PortFlowQueryDetails": {
            "type": "object",
            "properties": {
                "device_id": {
                    "type": "string"
                },
                "end_time": {
                    "type": "string"
                },
                "port_id": {
                    "type": "string"
                },
                "start_time": {
                    "type": "string"
                },
                "vni": {
                    "type": "string"
                }
            }
        },
        "traffic.PortFlowResponse": {
            "type": "object",
            "properties": {
                "flow_data": {
                    "$ref": "#/definitions/traffic.PortFlowData"
                },
                "query_details": {
                    "$ref": "#/definitions/traffic.PortFlowQueryDetails"
                },
                "request_id": {
                    "type": "string"
                }
            }
        },
        "traffic.PortHistoryData": {
            "type": "object",
            "properties": {
                "series": {
                    "description": "数据系列数组",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/traffic.TrafficSeries"
                    }
                },
                "timestamps": {
                    "description": "RFC3339 UTC 格式的时间戳数组",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "unit": {
                    "description": "数据单位 (e.g., \"Mbps\")",
                    "type": "string"
                }
            }
        },
        "traffic.PortHistoryQueryDetails": {
            "type": "object",
            "properties": {
                "device_id": {
                    "type": "string"
                },
                "end_time": {
                    "type": "string"
                },
                "port_id": {
                    "type": "string"
                },
                "start_time": {
                    "type": "string"
                },
                "step": {
                    "type": "string"
                },
                "vni": {
                    "type": "string"
                }
            }
        },
        "traffic.PortHistoryResponse": {
            "type": "object",
            "properties": {
                "history_data": {
                    "$ref": "#/definitions/traffic.PortHistoryData"
                },
                "query_details": {
                    "$ref": "#/definitions/traffic.PortHistoryQueryDetails"
                },
                "request_id": {
                    "type": "string"
                }
            }
        },
        "traffic.TrafficChartData": {
            "type": "object",
            "properties": {
                "series": {
                    "description": "数据系列数组",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/traffic.TrafficSeries"
                    }
                },
                "timestamps": {
                    "description": "RFC3339 UTC 格式的时间戳数组",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "unit": {
                    "description": "数据单位 (e.g., \"Mbps\", \"Kbps\")",
                    "type": "string"
                }
            }
        },
        "traffic.TrafficSeries": {
            "type": "object",
            "properties": {
                "data": {
                    "description": "与 Timestamps 对应的数值数组",
                    "type": "array",
                    "items": {
                        "type": "number"
                    }
                },
                "name": {
                    "description": "系列名称 (e.g., \"A端 (CE1-GE1/0/1) 平均入流量\")",
                    "type": "string"
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8080",
	BasePath:         "/",
	Schemes:          []string{"http", "https"},
	Title:            "DCI监控系统API",
	Description:      "DCI监控系统提供的告警、设备状态、流量监测等API接口",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
