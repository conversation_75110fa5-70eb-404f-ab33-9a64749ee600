---
title: |
  DCI告警联动数据库设计

subtitle: |
  告警系统联动字段扩展与相关表结构设计
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-01-27 | 顾铠羟 | 初始版本           |

# 1 概述

## 1.1 设计背景

当前`monitor_alert`表设计主要专注于告警本身的管理，缺乏与其他系统联动的必要字段。为支撑告警系统与网络自动化系统、网络控制系统等业务系统的协同工作，需要对数据库结构进行扩展设计。

## 1.2 设计目标

* 扩展`monitor_alert`表，增加联动相关字段
* 设计联动策略配置表，支持灵活的联动规则管理
* 设计联动操作历史表，记录联动操作的完整轨迹
* 设计联动状态跟踪表，实时跟踪联动操作状态
* 确保数据库设计的性能、可扩展性和数据一致性

## 1.3 设计原则

* **向后兼容**：保持现有表结构的兼容性
* **性能优化**：为联动字段建立合适的索引
* **数据完整性**：通过外键约束确保数据一致性
* **扩展性**：预留扩展字段支持未来需求

# 2 monitor_alert表字段扩展

## 2.1 现有表结构回顾

```sql
-- 现有monitor_alert表结构
CREATE TABLE monitor_alert (
    id VARCHAR(64) PRIMARY KEY COMMENT '告警ID',
    name VARCHAR(255) NOT NULL COMMENT '告警名称',
    source VARCHAR(100) NOT NULL COMMENT '告警来源',
    level ENUM('critical', 'warning', 'info') NOT NULL COMMENT '告警级别',
    status ENUM('active', 'acknowledged', 'resolved') NOT NULL DEFAULT 'active' COMMENT '告警状态',
    description TEXT COMMENT '告警描述',
    starts_at TIMESTAMP NOT NULL COMMENT '告警开始时间',
    ends_at TIMESTAMP NULL COMMENT '告警结束时间',
    notification_due_at TIMESTAMP NULL COMMENT '通知到期时间',
    resolved_at TIMESTAMP NULL COMMENT '告警解决时间',
    acknowledged_by VARCHAR(100) NULL COMMENT '确认人',
    acknowledged_at TIMESTAMP NULL COMMENT '确认时间',
    note TEXT COMMENT '备注信息',
    rule_id VARCHAR(64) NULL COMMENT '关联的告警规则ID',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
);
```

## 2.2 联动字段扩展

### 2.2.1 网络自动化任务联动字段

```sql
-- 网络自动化任务联动字段
ALTER TABLE monitor_alert 
ADD COLUMN task_session_id VARCHAR(64) NULL COMMENT '关联网络自动化任务会话ID',
ADD COLUMN task_phase ENUM('prep', 'exec', 'post') NULL COMMENT '告警发生的任务阶段',
ADD COLUMN task_correlation_type ENUM('caused_by', 'triggered_during', 'resolved_by') NULL COMMENT '与任务的关联类型',
ADD COLUMN task_start_time TIMESTAMP NULL COMMENT '关联任务开始时间',
ADD COLUMN task_end_time TIMESTAMP NULL COMMENT '关联任务结束时间';
```

### 2.2.2 配置变更联动字段

```sql
-- 配置变更联动字段
ALTER TABLE monitor_alert 
ADD COLUMN config_change_id VARCHAR(64) NULL COMMENT '关联具体的配置变更记录',
ADD COLUMN change_correlation_score DECIMAL(3,2) NULL COMMENT '与配置变更的相关性评分',
ADD COLUMN change_impact_scope JSON NULL COMMENT '配置变更的影响范围',
ADD COLUMN change_rollback_status ENUM('none', 'attempted', 'successful', 'failed') DEFAULT 'none' COMMENT '回滚状态';
```

### 2.2.3 网络控制联动字段

```sql
-- 网络控制联动字段
ALTER TABLE monitor_alert 
ADD COLUMN automation_action_id VARCHAR(64) NULL COMMENT '关联的自动化操作ID',
ADD COLUMN remediation_task_id VARCHAR(64) NULL COMMENT '关联的修复任务ID',
ADD COLUMN escalation_policy_id VARCHAR(64) NULL COMMENT '关联的升级策略ID',
ADD COLUMN control_response_time INT NULL COMMENT '控制响应时间(毫秒)',
ADD COLUMN control_status ENUM('pending', 'executing', 'completed', 'failed') DEFAULT 'pending' COMMENT '控制操作状态',
ADD COLUMN control_result JSON NULL COMMENT '控制操作结果',
ADD COLUMN control_error_message TEXT NULL COMMENT '控制操作错误信息';
```

### 2.2.4 因果关系分析字段

```sql
-- 因果关系分析字段
ALTER TABLE monitor_alert 
ADD COLUMN correlation_group_id VARCHAR(64) NULL COMMENT '关联告警组ID',
ADD COLUMN root_cause_alert_id VARCHAR(64) NULL COMMENT '根因告警ID',
ADD COLUMN trigger_context JSON NULL COMMENT '触发上下文信息',
ADD COLUMN impact_scope JSON NULL COMMENT '影响范围定义';
```

## 2.3 联动索引设计

```sql
-- 任务联动索引
CREATE INDEX idx_alert_task_session ON monitor_alert(task_session_id);
CREATE INDEX idx_alert_task_phase ON monitor_alert(task_phase, task_session_id);
CREATE INDEX idx_alert_task_correlation ON monitor_alert(task_correlation_type, task_session_id);

-- 配置变更联动索引
CREATE INDEX idx_alert_config_change ON monitor_alert(config_change_id);
CREATE INDEX idx_alert_correlation_score ON monitor_alert(change_correlation_score DESC);
CREATE INDEX idx_alert_rollback_status ON monitor_alert(change_rollback_status);

-- 网络控制联动索引
CREATE INDEX idx_alert_automation_action ON monitor_alert(automation_action_id);
CREATE INDEX idx_alert_remediation_task ON monitor_alert(remediation_task_id);
CREATE INDEX idx_alert_control_status ON monitor_alert(control_status);
CREATE INDEX idx_alert_level_control ON monitor_alert(level, control_status);

-- 因果关系索引
CREATE INDEX idx_alert_correlation_group ON monitor_alert(correlation_group_id);
CREATE INDEX idx_alert_root_cause ON monitor_alert(root_cause_alert_id);

-- 复合索引
CREATE INDEX idx_alert_level_status_time ON monitor_alert(level, status, starts_at);
CREATE INDEX idx_alert_source_level ON monitor_alert(source, level);
```

# 3 联动策略配置表设计

## 3.1 alert_remediation_policy表

```sql
-- 告警修复策略配置表
CREATE TABLE alert_remediation_policy (
    id VARCHAR(64) PRIMARY KEY COMMENT '策略ID',
    name VARCHAR(255) NOT NULL COMMENT '策略名称',
    description TEXT COMMENT '策略描述',
    alert_level ENUM('critical', 'warning', 'info') NOT NULL COMMENT '适用告警级别',
    alert_type VARCHAR(100) NOT NULL COMMENT '适用告警类型',
    auto_remediate BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否自动修复',
    response_time INT NOT NULL DEFAULT 30 COMMENT '响应时间(秒)',
    escalation_threshold INT NOT NULL DEFAULT 300 COMMENT '升级阈值(秒)',
    actions JSON NOT NULL COMMENT '修复操作配置',
    conditions JSON NULL COMMENT '触发条件配置',
    priority INT NOT NULL DEFAULT 1 COMMENT '策略优先级',
    enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_policy_level_type (alert_level, alert_type),
    INDEX idx_policy_enabled (enabled),
    INDEX idx_policy_priority (priority DESC)
) COMMENT '告警修复策略配置表';
```

## 3.2 alert_escalation_policy表

```sql
-- 告警升级策略配置表
CREATE TABLE alert_escalation_policy (
    id VARCHAR(64) PRIMARY KEY COMMENT '升级策略ID',
    name VARCHAR(255) NOT NULL COMMENT '策略名称',
    description TEXT COMMENT '策略描述',
    alert_level ENUM('critical', 'warning', 'info') NOT NULL COMMENT '适用告警级别',
    escalation_level INT NOT NULL DEFAULT 1 COMMENT '升级级别',
    escalation_time INT NOT NULL DEFAULT 300 COMMENT '升级时间(秒)',
    escalation_actions JSON NOT NULL COMMENT '升级操作配置',
    notification_channels JSON NULL COMMENT '通知渠道配置',
    enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_escalation_level_type (alert_level, escalation_level),
    INDEX idx_escalation_enabled (enabled)
) COMMENT '告警升级策略配置表';
```

# 4 联动操作历史表设计

## 4.1 alert_control_history表

```sql
-- 告警控制操作历史表
CREATE TABLE alert_control_history (
    id VARCHAR(64) PRIMARY KEY COMMENT '操作历史ID',
    alert_id VARCHAR(64) NOT NULL COMMENT '关联告警ID',
    action_id VARCHAR(64) NOT NULL COMMENT '操作ID',
    action_type VARCHAR(100) NOT NULL COMMENT '操作类型',
    action_params JSON NULL COMMENT '操作参数',
    status ENUM('pending', 'executing', 'completed', 'failed') NOT NULL COMMENT '操作状态',
    result JSON NULL COMMENT '操作结果',
    error_message TEXT NULL COMMENT '错误信息',
    response_time INT NULL COMMENT '响应时间(毫秒)',
    executed_at TIMESTAMP NULL COMMENT '执行时间',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_history_alert_id (alert_id),
    INDEX idx_history_action_id (action_id),
    INDEX idx_history_status (status),
    INDEX idx_history_executed_at (executed_at),
    FOREIGN KEY (alert_id) REFERENCES monitor_alert(id) ON DELETE CASCADE
) COMMENT '告警控制操作历史表';
```

## 4.2 alert_correlation_history表

```sql
-- 告警关联历史表
CREATE TABLE alert_correlation_history (
    id VARCHAR(64) PRIMARY KEY COMMENT '关联历史ID',
    alert_id VARCHAR(64) NOT NULL COMMENT '告警ID',
    correlation_type ENUM('task', 'config_change', 'automation_action') NOT NULL COMMENT '关联类型',
    correlation_id VARCHAR(64) NOT NULL COMMENT '关联对象ID',
    correlation_score DECIMAL(3,2) NULL COMMENT '关联评分',
    correlation_context JSON NULL COMMENT '关联上下文',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_correlation_alert_id (alert_id),
    INDEX idx_correlation_type_id (correlation_type, correlation_id),
    INDEX idx_correlation_score (correlation_score DESC),
    FOREIGN KEY (alert_id) REFERENCES monitor_alert(id) ON DELETE CASCADE
) COMMENT '告警关联历史表';
```

# 5 联动状态跟踪表设计

## 5.1 alert_remediation_status表

```sql
-- 告警修复状态跟踪表
CREATE TABLE alert_remediation_status (
    id VARCHAR(64) PRIMARY KEY COMMENT '状态跟踪ID',
    alert_id VARCHAR(64) NOT NULL COMMENT '关联告警ID',
    remediation_task_id VARCHAR(64) NOT NULL COMMENT '修复任务ID',
    status ENUM('pending', 'executing', 'completed', 'failed', 'cancelled') NOT NULL COMMENT '修复状态',
    current_step VARCHAR(100) NULL COMMENT '当前步骤',
    progress INT NOT NULL DEFAULT 0 COMMENT '进度百分比',
    start_time TIMESTAMP NULL COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    duration INT NULL COMMENT '持续时间(秒)',
    result_summary TEXT NULL COMMENT '结果摘要',
    error_details TEXT NULL COMMENT '错误详情',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_status_alert_id (alert_id),
    INDEX idx_status_task_id (remediation_task_id),
    INDEX idx_status_status (status),
    INDEX idx_status_start_time (start_time),
    FOREIGN KEY (alert_id) REFERENCES monitor_alert(id) ON DELETE CASCADE
) COMMENT '告警修复状态跟踪表';
```

## 5.2 alert_escalation_status表

```sql
-- 告警升级状态跟踪表
CREATE TABLE alert_escalation_status (
    id VARCHAR(64) PRIMARY KEY COMMENT '升级状态ID',
    alert_id VARCHAR(64) NOT NULL COMMENT '关联告警ID',
    escalation_policy_id VARCHAR(64) NOT NULL COMMENT '升级策略ID',
    escalation_level INT NOT NULL COMMENT '当前升级级别',
    status ENUM('pending', 'executing', 'completed', 'failed') NOT NULL COMMENT '升级状态',
    triggered_at TIMESTAMP NULL COMMENT '触发时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    notification_sent BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已发送通知',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_escalation_alert_id (alert_id),
    INDEX idx_escalation_policy_id (escalation_policy_id),
    INDEX idx_escalation_status (status),
    INDEX idx_escalation_triggered_at (triggered_at),
    FOREIGN KEY (alert_id) REFERENCES monitor_alert(id) ON DELETE CASCADE,
    FOREIGN KEY (escalation_policy_id) REFERENCES alert_escalation_policy(id) ON DELETE CASCADE
) COMMENT '告警升级状态跟踪表';
```

# 6 数据一致性约束

## 6.1 外键约束设计

```sql
-- monitor_alert表外键约束
ALTER TABLE monitor_alert 
ADD CONSTRAINT fk_alert_root_cause 
FOREIGN KEY (root_cause_alert_id) REFERENCES monitor_alert(id) ON DELETE SET NULL;

-- alert_control_history表外键约束
ALTER TABLE alert_control_history 
ADD CONSTRAINT fk_control_history_alert 
FOREIGN KEY (alert_id) REFERENCES monitor_alert(id) ON DELETE CASCADE;

-- alert_correlation_history表外键约束
ALTER TABLE alert_correlation_history 
ADD CONSTRAINT fk_correlation_history_alert 
FOREIGN KEY (alert_id) REFERENCES monitor_alert(id) ON DELETE CASCADE;

-- alert_remediation_status表外键约束
ALTER TABLE alert_remediation_status 
ADD CONSTRAINT fk_remediation_status_alert 
FOREIGN KEY (alert_id) REFERENCES monitor_alert(id) ON DELETE CASCADE;

-- alert_escalation_status表外键约束
ALTER TABLE alert_escalation_status 
ADD CONSTRAINT fk_escalation_status_alert 
FOREIGN KEY (alert_id) REFERENCES monitor_alert(id) ON DELETE CASCADE,
ADD CONSTRAINT fk_escalation_status_policy 
FOREIGN KEY (escalation_policy_id) REFERENCES alert_escalation_policy(id) ON DELETE CASCADE;
```

## 6.2 检查约束设计

```sql
-- monitor_alert表检查约束
ALTER TABLE monitor_alert 
ADD CONSTRAINT chk_alert_correlation_score 
CHECK (change_correlation_score >= 0 AND change_correlation_score <= 1),
ADD CONSTRAINT chk_alert_control_response_time 
CHECK (control_response_time >= 0),
ADD CONSTRAINT chk_alert_task_times 
CHECK (task_start_time IS NULL OR task_end_time IS NULL OR task_start_time <= task_end_time);

-- alert_remediation_policy表检查约束
ALTER TABLE alert_remediation_policy 
ADD CONSTRAINT chk_policy_response_time 
CHECK (response_time > 0),
ADD CONSTRAINT chk_policy_escalation_threshold 
CHECK (escalation_threshold > 0),
ADD CONSTRAINT chk_policy_priority 
CHECK (priority >= 1 AND priority <= 10);

-- alert_escalation_policy表检查约束
ALTER TABLE alert_escalation_policy 
ADD CONSTRAINT chk_escalation_level 
CHECK (escalation_level >= 1),
ADD CONSTRAINT chk_escalation_time 
CHECK (escalation_time > 0);

-- alert_remediation_status表检查约束
ALTER TABLE alert_remediation_status 
ADD CONSTRAINT chk_status_progress 
CHECK (progress >= 0 AND progress <= 100),
ADD CONSTRAINT chk_status_duration 
CHECK (duration IS NULL OR duration >= 0);
```

# 7 数据分区策略

## 7.1 时间分区设计

```sql
-- monitor_alert表按时间分区
ALTER TABLE monitor_alert 
PARTITION BY RANGE (UNIX_TIMESTAMP(starts_at)) (
    PARTITION p2024_01 VALUES LESS THAN (UNIX_TIMESTAMP('2024-02-01 00:00:00')),
    PARTITION p2024_02 VALUES LESS THAN (UNIX_TIMESTAMP('2024-03-01 00:00:00')),
    PARTITION p2024_03 VALUES LESS THAN (UNIX_TIMESTAMP('2024-04-01 00:00:00')),
    -- 继续添加更多分区...
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- alert_control_history表按时间分区
ALTER TABLE alert_control_history 
PARTITION BY RANGE (UNIX_TIMESTAMP(created_at)) (
    PARTITION p2024_01 VALUES LESS THAN (UNIX_TIMESTAMP('2024-02-01 00:00:00')),
    PARTITION p2024_02 VALUES LESS THAN (UNIX_TIMESTAMP('2024-03-01 00:00:00')),
    PARTITION p2024_03 VALUES LESS THAN (UNIX_TIMESTAMP('2024-04-01 00:00:00')),
    -- 继续添加更多分区...
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

## 7.2 分区管理策略

* **自动分区创建**：每月自动创建新的分区
* **历史分区归档**：超过保留期的分区自动归档
* **分区索引优化**：为每个分区建立本地索引
* **分区统计更新**：定期更新分区统计信息

# 8 性能优化策略

## 8.1 索引优化

### 8.1.1 复合索引设计

```sql
-- 告警查询复合索引
CREATE INDEX idx_alert_composite_query ON monitor_alert(level, status, starts_at, source);

-- 联动查询复合索引
CREATE INDEX idx_alert_linkage_query ON monitor_alert(task_session_id, control_status, level);

-- 时间范围查询索引
CREATE INDEX idx_alert_time_range ON monitor_alert(starts_at, ends_at, status);
```

### 8.1.2 覆盖索引设计

```sql
-- 告警列表查询覆盖索引
CREATE INDEX idx_alert_list_covering ON monitor_alert(level, status, starts_at) 
INCLUDE (id, name, source, description);

-- 联动状态查询覆盖索引
CREATE INDEX idx_alert_linkage_covering ON monitor_alert(task_session_id, control_status) 
INCLUDE (id, level, automation_action_id, remediation_task_id);
```

## 8.2 查询优化

### 8.2.1 查询重写规则

* **时间范围优化**：使用时间分区减少扫描范围
* **状态过滤优化**：优先使用状态索引进行过滤
* **联动查询优化**：使用联动字段索引加速关联查询
* **分页查询优化**：使用覆盖索引减少回表操作

### 8.2.2 缓存策略

* **策略配置缓存**：缓存修复策略和升级策略配置
* **状态信息缓存**：缓存告警状态和联动状态信息
* **查询结果缓存**：缓存频繁查询的结果
* **统计信息缓存**：缓存告警统计和联动统计信息

# 9 数据维护策略

## 9.1 数据清理策略

```sql
-- 历史告警数据清理
DELETE FROM monitor_alert 
WHERE status = 'resolved' 
AND resolved_at < DATE_SUB(NOW(), INTERVAL 1 YEAR);

-- 历史联动记录清理
DELETE FROM alert_control_history 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 6 MONTH);

-- 历史关联记录清理
DELETE FROM alert_correlation_history 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 6 MONTH);
```

## 9.2 数据归档策略

* **告警数据归档**：超过1年的已解决告警归档到历史表
* **联动记录归档**：超过6个月的联动记录归档到历史表
* **统计信息归档**：定期归档告警统计和联动统计信息
* **配置历史归档**：定期归档策略配置的历史版本

# 10 监控与维护

## 10.1 数据库监控指标

* **表大小监控**：监控各表的数据量和增长趋势
* **索引使用率**：监控索引的使用情况和效率
* **查询性能**：监控慢查询和查询响应时间
* **锁等待时间**：监控数据库锁等待情况

## 10.2 数据质量监控

* **数据完整性**：监控外键约束和数据一致性
* **数据准确性**：监控联动字段的数据准确性
* **数据及时性**：监控数据更新的及时性
* **数据有效性**：监控数据格式和值的有效性

# 11 总结

本数据库设计为DCI告警系统提供了完整的联动数据存储方案。通过扩展`monitor_alert`表字段、设计联动配置表、操作历史表和状态跟踪表，构建了完整的联动数据模型。该设计具有良好的性能、可扩展性和可维护性，能够支撑告警系统与其他业务系统的协同工作。 