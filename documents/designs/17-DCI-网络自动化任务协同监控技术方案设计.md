---
title: |
  DCI-网络自动化任务协同监控技术方案设计

subtitle: |
  数据监测系统与网络自动化系统协同工作的技术实现
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-05-19 | 顾铠羟 | 初始版本           |
| V1.1 | 2025-05-19 | 顾铠羟 | 补充任务监控增强模块、任务追踪与审计模块和报告生成与分发模块的详细设计 |

# 1 文档介绍

## 1.1 文档目的

本文档详细设计DCI数据监测系统中网络自动化任务协同监控功能的技术实现方案，包括系统架构、数据流程、接口设计和实现细节。此方案旨在实现数据监测系统与网络自动化控制系统的高效协同，为网络配置变更提供全方位的监控支持和影响评估。

## 1.2 文档范围

本文档涵盖网络自动化任务协同监控的以下内容：

- 任务信号接收与处理机制
- 任务监控会话创建与管理
- 任务相关数据采集与分析流程
- 任务报告生成与展示
- 与网络自动化控制系统的接口定义

# 2 总体设计

## 2.1 设计目标

网络自动化任务协同监控功能的核心设计目标包括：

1. 实现与网络自动化控制系统的无缝集成，准确接收任务信号
2. 提供任务执行期间的网络状态实时监控能力
3. 收集与任务相关的系统日志、设备日志和性能指标
4. 生成任务执行影响分析报告，评估配置变更对网络的影响
5. 支持任务异常中断和回滚场景的监控处理

## 2.2 架构设计

网络自动化任务协同监控功能的架构设计如下：

```mermaid
graph TD
    subgraph 网络自动化控制系统
        NetAutoCtrl[自动化控制服务]
    end
    
    subgraph 数据监测系统
        subgraph 任务协同模块
            TaskReceiver[任务信号接收器]
            TaskSession[任务会话管理器]
            DataCollector[相关数据收集器]
            ReportGen[报告生成器]
        end
        
        subgraph 核心服务
            MetricService[指标服务]
            LogService[日志服务]
            AlertService[告警服务]
        end
        
        subgraph 存储层
            TSDB[(时序数据库<br/>Prometheus/Thanos)]
            ES[(日志存储)]
            MySQL[(元数据库)]
            ReportStore[(报告存储)]
        end
    end
    
    NetAutoCtrl -->|任务信号| TaskReceiver
    TaskReceiver -->|创建会话| TaskSession
    TaskSession -->|查询相关指标| MetricService
    TaskSession -->|查询相关日志| LogService
    TaskSession -->|关联告警| AlertService
    
    MetricService -->|读取数据| TSDB
    LogService -->|读取数据| ES
    AlertService -->|读取/写入| MySQL
    
    TaskSession -->|收集数据| DataCollector
    DataCollector -->|分析数据| ReportGen
    ReportGen -->|存储报告| ReportStore
    
    classDef primary fill:#d9f,stroke:#333,stroke-width:1px
    classDef secondary fill:#bbf,stroke:#333,stroke-width:1px
    classDef storage fill:#ffd,stroke:#333,stroke-width:1px
    
    class TaskReceiver,TaskSession,DataCollector,ReportGen primary
    class MetricService,LogService,AlertService secondary
    class TSDB,ES,MySQL,ReportStore storage
```

## 2.3 数据流程图

网络自动化任务协同监控的主要数据流程如下：

```mermaid
sequenceDiagram
    participant NetAutoCtrl as 网络自动化控制系统
    participant TaskReceiver as 任务信号接收器
    participant TaskSession as 任务会话管理器
    participant DataCollector as 数据收集器
    participant MetricService as 指标服务
    participant LogService as 日志服务
    participant ReportGen as 报告生成器
    participant UI as 监控界面
    
    NetAutoCtrl->>TaskReceiver: 发送任务开始信号(TaskID, 目标设备列表)
    TaskReceiver->>TaskSession: 创建任务监控会话
    TaskSession->>TaskSession: 记录任务元数据
    TaskSession->>DataCollector: 启动相关数据收集
    
    loop 任务执行期间
        DataCollector->>MetricService: 查询相关设备指标
        MetricService-->>DataCollector: 返回指标数据
        DataCollector->>LogService: 查询相关设备日志
        LogService-->>DataCollector: 返回日志数据
        DataCollector->>TaskSession: 存储收集的数据
    end
    
    NetAutoCtrl->>TaskReceiver: 发送任务结束信号(TaskID, 结果状态)
    TaskReceiver->>TaskSession: 更新任务状态
    TaskSession->>DataCollector: 停止数据收集
    TaskSession->>ReportGen: 请求生成报告
    
    ReportGen->>ReportGen: 分析任务数据
    ReportGen->>TaskSession: 存储报告引用
    ReportGen-->>UI: 通知报告可用
```

## 2.4 模块化设计

网络自动化任务协同监控功能由以下核心模块组成：

1. **任务信号接收器**：独立服务，负责接收和解析来自网络自动化控制系统的任务信号
2. **任务会话管理器**：核心组件，负责任务监控会话的创建、更新和关闭
3. **相关数据收集器**：内部组件，由任务会话管理器控制，负责收集与任务相关的数据
4. **报告生成器**：独立服务，负责分析任务数据并生成影响评估报告

## 2.5 技术选型

1. **消息队列**：Kafka，用于任务信号传递和事件通知
2. **数据存储**：
   - Prometheus/Thanos：存储任务相关的时序指标数据
   - Elasticsearch：存储任务相关的日志和事件数据
   - MySQL：存储任务元数据和报告索引
3. **API框架**：Go语言的Gin框架，提供RESTful API接口
4. **报告生成**：基于Go模板引擎，支持HTML和PDF格式输出

# 3 详细设计

## 3.1 功能模块

### 3.1.1 任务信号接收器

任务信号接收器负责接收来自网络自动化控制系统的任务信号，并将其转发给任务会话管理器。

**主要功能**：
- 提供REST API接口，接收任务开始、结束和异常信号
- 支持Kafka消息队列方式接收任务信号
- 验证任务信号的合法性和完整性
- 将任务信号转换为标准内部格式
- 将处理后的任务信号发送给任务会话管理器

**接口定义**：
- REST API：`POST /api/v1/tasks/signals`
- Kafka Topic：`dci.task.signals`

**消息格式**：
```json
{
  "signal_type": "start|end|abort",
  "task_id": "uuid-string",
  "timestamp": "ISO8601-timestamp",
  "task_type": "configuration|upgrade|rollback",
  "target_devices": ["device1", "device2"],
  "expected_duration": 300,
  "status": "success|failed|aborted",
  "metadata": {
    "key1": "value1",
    "key2": "value2"
  }
}
```

### 3.1.2 任务会话管理器

任务会话管理器负责创建和管理任务监控会话，协调数据收集和报告生成过程。

**主要功能**：
- 创建新的任务监控会话
- 维护任务状态和元数据
- 协调相关数据收集器的工作
- 触发报告生成流程
- 提供任务查询和状态更新接口

**任务状态流转**：
```mermaid
stateDiagram-v2
    [*] --> Pending: 接收任务信号
    Pending --> Monitoring: 开始监控
    Monitoring --> Completed: 任务正常结束
    Monitoring --> Failed: 任务异常结束
    Monitoring --> Aborted: 任务中止
    Completed --> [*]
    Failed --> [*]
    Aborted --> [*]
```

**数据模型**：
```sql
-- MySQL数据库表定义
CREATE TABLE task_sessions (
    id VARCHAR(36) PRIMARY KEY,
    task_id VARCHAR(36) NOT NULL,
    status ENUM('pending', 'monitoring', 'completed', 'failed', 'aborted') NOT NULL,
    start_time DATETIME,
    end_time DATETIME,
    task_type VARCHAR(50),
    target_devices JSON,
    metadata JSON,
    report_id VARCHAR(36),
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);
```

### 3.1.3 相关数据收集器

相关数据收集器负责收集与任务相关的各类数据，包括性能指标、日志和事件。

**主要功能**：
- 根据任务目标设备列表确定监控范围
- 从Prometheus/Thanos查询相关性能指标
- 从日志存储查询相关日志和事件
- 关联任务期间触发的告警
- 将收集的数据存储到任务数据集中

**数据收集策略**：
- **性能指标**：
  - 设备CPU使用率
  - 设备内存使用率
  - 接口流量和错误计数
  - 链路状态变化
- **日志和事件**：
  - 配置变更日志
  - 接口状态变化日志
  - 系统错误日志
  - 安全相关日志
- **告警信息**：
  - 任务期间触发的所有告警
  - 告警级别和状态

### 3.1.4 报告生成器

报告生成器负责分析任务数据并生成影响评估报告。

**主要功能**：
- 分析任务执行前后的性能指标变化
- 识别任务执行期间的异常事件
- 评估配置变更对网络的影响
- 生成结构化的任务报告
- 支持多种报告格式（HTML、PDF）

**报告内容结构**：
1. **任务概述**：
   - 任务ID和类型
   - 执行时间和持续时间
   - 目标设备列表
   - 任务结果状态
2. **性能影响分析**：
   - 关键指标变化图表
   - 异常指标标记
   - 性能趋势分析
3. **日志分析**：
   - 关键事件时间线
   - 错误和警告日志摘要
   - 配置变更日志分析
4. **告警分析**：
   - 任务期间触发的告警列表
   - 告警统计和分类
5. **总体评估**：
   - 变更影响评级
   - 潜在问题和建议
   - 后续监控建议


**报告存储**：
```sql
-- MySQL数据库表定义
CREATE TABLE task_reports (
    id VARCHAR(36) PRIMARY KEY,
    task_session_id VARCHAR(36) NOT NULL,
    report_type VARCHAR(20) NOT NULL,
    title VARCHAR(255) NOT NULL,
    content_path VARCHAR(255) NOT NULL,
    summary TEXT,
    impact_level ENUM('none', 'low', 'medium', 'high', 'critical'),
    created_at DATETIME NOT NULL,
    FOREIGN KEY (task_session_id) REFERENCES task_sessions(id)
);
```

### 3.1.5 任务监控增强模块

任务监控增强模块负责在任务执行期间提供更精细的监控和分析能力，以便更准确地评估网络变更影响。

**主要功能**：
- 在任务执行期间动态调整相关设备的监控频率
- 建立变更前后的动态基线指标对比
- 联动告警检测规则，实时监测配置变更影响
- 自动补齐监控任务全生命周期关键节点

**监控频率调整**：
```mermaid
sequenceDiagram
    participant TaskSession as 任务会话管理器
    participant Enhancer as 监控增强模块
    participant Collector as 数据采集器
    participant DB as Prometheus/Thanos
    
    TaskSession->>Enhancer: 启动监控增强
    Enhancer->>Enhancer: 确定目标设备和指标
    Enhancer->>Collector: 请求提高采集频率
    
    Note over Collector: 调整采集频率
    Note over Collector: 正常频率: 60s/次
    Note over Collector: 增强频率: 15s/次
    
    loop 任务执行期间
        Collector->>DB: 高频率写入数据
    end
    
    TaskSession->>Enhancer: 结束监控增强
    Enhancer->>Collector: 恢复正常采集频率
```

**动态基线对比**：
- **基线建立**：
  - 任务开始前60分钟的性能指标作为前基线
  - 任务结束后30分钟的性能指标作为后基线
- **对比指标**：
  - 设备CPU使用率平均值、峰值和波动范围
  - 内存占用率平均值、峰值和波动范围
  - 网络接口流量平均值、峰值和波动范围
  - 网络接口错误计数变化
  - 链路状态变化频率
- **异常判定**：
  - 变更后指标超出变更前基线的3个标准差
  - 变更后出现变更前未出现的错误类型
  - 变更后链路状态抖动频率显著增加

**数据模型**：
```sql
-- MySQL数据库表定义
CREATE TABLE task_enhanced_metrics (
    id VARCHAR(36) PRIMARY KEY COMMENT '记录唯一标识符',
    task_session_id VARCHAR(36) NOT NULL COMMENT '关联任务会话ID',
    device_id VARCHAR(36) NOT NULL COMMENT '设备ID',
    metric_name VARCHAR(100) NOT NULL COMMENT '指标名称',
    collection_phase ENUM('pre_baseline', 'during_task', 'post_baseline') NOT NULL COMMENT '采集阶段',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',
    avg_value DOUBLE COMMENT '平均值',
    max_value DOUBLE COMMENT '最大值',
    min_value DOUBLE COMMENT '最小值',
    std_dev DOUBLE COMMENT '标准差',
    sample_count INT COMMENT '样本数量',
    anomaly_detected BOOLEAN DEFAULT FALSE COMMENT '是否检测到异常',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    FOREIGN KEY (task_session_id) REFERENCES task_sessions(id),
    INDEX idx_task_device_metric (task_session_id, device_id, metric_name),
    INDEX idx_collection_phase (collection_phase)
);
```

**生命周期关键节点**：
1. **前基线阶段**：任务开始前60分钟，采集正常频率的基线数据
2. **准备阶段**：任务开始前5分钟，提高采集频率
3. **执行阶段**：任务执行期间，维持高频采集
4. **稳定阶段**：任务结束后15分钟，维持高频采集
5. **后基线阶段**：任务结束后15-45分钟，恢复正常频率，采集后基线数据

**与告警系统联动**：
- 在任务执行期间，临时降低相关设备的告警阈值敏感度，避免因预期中的短暂波动触发大量告警
- 对任务相关设备的告警进行特殊标记，便于在报告中关联分析
- 提供任务期间触发的告警与历史同时段告警的对比分析

### 3.1.6 任务追踪与审计模块

任务追踪与审计模块负责记录任务执行的完整时间线和相关事件，支持后续的审计和根因分析。

**主要功能**：
- 为每个自动化任务生成唯一标识符，贯穿整个监控过程
- 记录任务执行的详细时间线，包括开始时间、结束时间和关键节点时间戳
- 建立配置变更与网络事件的关联关系，支持根因分析
- 提供任务执行历史查询功能，支持按时间、设备、结果等条件筛选
- 实现任务监控数据的长期归档，便于后期审计和分析

**时间线记录**：
```mermaid
sequenceDiagram
    participant Task as 自动化任务
    participant Tracker as 追踪模块
    participant Timeline as 时间线存储
    participant Event as 事件存储
    
    Task->>Tracker: 任务开始信号
    Tracker->>Timeline: 记录开始事件
    
    loop 任务执行期间
        Task->>Tracker: 阶段完成信号
        Tracker->>Timeline: 记录阶段事件
        Tracker->>Event: 关联网络事件
    end
    
    Task->>Tracker: 任务结束信号
    Tracker->>Timeline: 记录结束事件
    Tracker->>Tracker: 生成完整时间线
```

**数据模型**：
```sql
-- MySQL数据库表定义
CREATE TABLE task_timeline_events (
    id VARCHAR(36) PRIMARY KEY COMMENT '事件唯一标识符',
    task_session_id VARCHAR(36) NOT NULL COMMENT '关联任务会话ID',
    event_type VARCHAR(50) NOT NULL COMMENT '事件类型',
    event_time DATETIME NOT NULL COMMENT '事件时间',
    event_source VARCHAR(50) NOT NULL COMMENT '事件来源',
    event_description TEXT COMMENT '事件描述',
    related_device VARCHAR(36) COMMENT '相关设备ID',
    related_config VARCHAR(255) COMMENT '相关配置项',
    metadata JSON COMMENT '事件元数据',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    FOREIGN KEY (task_session_id) REFERENCES task_sessions(id),
    INDEX idx_task_event_time (task_session_id, event_time),
    INDEX idx_event_type (event_type),
    INDEX idx_related_device (related_device)
);

CREATE TABLE task_config_changes (
    id VARCHAR(36) PRIMARY KEY COMMENT '配置变更唯一标识符',
    task_session_id VARCHAR(36) NOT NULL COMMENT '关联任务会话ID',
    device_id VARCHAR(36) NOT NULL COMMENT '设备ID',
    config_path VARCHAR(255) NOT NULL COMMENT '配置路径',
    change_type ENUM('add', 'modify', 'delete') NOT NULL COMMENT '变更类型',
    old_value TEXT COMMENT '变更前值',
    new_value TEXT COMMENT '变更后值',
    change_time DATETIME NOT NULL COMMENT '变更时间',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    FOREIGN KEY (task_session_id) REFERENCES task_sessions(id),
    INDEX idx_task_device (task_session_id, device_id),
    INDEX idx_change_time (change_time)
);

CREATE TABLE task_network_events (
    id VARCHAR(36) PRIMARY KEY COMMENT '网络事件唯一标识符',
    task_session_id VARCHAR(36) NOT NULL COMMENT '关联任务会话ID',
    event_type VARCHAR(50) NOT NULL COMMENT '事件类型',
    device_id VARCHAR(36) NOT NULL COMMENT '设备ID',
    interface_id VARCHAR(36) COMMENT '接口ID',
    event_time DATETIME NOT NULL COMMENT '事件时间',
    event_severity ENUM('info', 'warning', 'error', 'critical') NOT NULL COMMENT '事件严重性',
    event_message TEXT NOT NULL COMMENT '事件消息',
    correlation_id VARCHAR(36) COMMENT '关联ID',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    FOREIGN KEY (task_session_id) REFERENCES task_sessions(id),
    INDEX idx_task_event_time (task_session_id, event_time),
    INDEX idx_device_interface (device_id, interface_id),
    INDEX idx_event_severity (event_severity)
);
```

**配置变更与网络事件关联分析**：
- **时间相关性分析**：
  - 识别配置变更后一定时间窗口内（如30秒）发生的网络事件
  - 计算配置变更与网络事件的时间相关性得分
- **设备关联分析**：
  - 直接关联：分析配置变更设备上发生的网络事件
  - 间接关联：分析与配置变更设备有拓扑连接的设备上发生的网络事件
- **配置项关联分析**：
  - 基于规则库，识别特定配置项变更可能影响的网络功能
  - 将配置变更与相应功能领域的网络事件关联

**审计查询功能**：
- 支持按任务ID、时间范围、设备、事件类型等多维度查询
- 提供任务执行历史的时间线可视化展示
- 支持导出审计日志和时间线报告
- 实现任务比较功能，对比不同任务的执行情况和影响

### 3.1.7 报告生成与分发模块

报告生成与分发模块负责分析任务数据并生成影响评估报告，同时提供报告的自动分发功能。

**主要功能**：
- 分析任务执行前后的性能指标变化
- 识别任务执行期间的异常事件
- 评估配置变更对网络的影响
- 生成结构化的任务报告
- 支持多种报告格式（HTML、PDF、Docx）
- 提供报告的自动分发功能

**报告生成流程**：
```mermaid
sequenceDiagram
    participant TaskSession as 任务会话管理器
    participant Generator as 报告生成器
    participant DataService as 数据服务
    participant Template as 模板引擎
    participant Storage as 报告存储
    participant Notifier as 通知服务
    
    TaskSession->>Generator: 请求生成报告
    Generator->>DataService: 获取任务元数据
    Generator->>DataService: 获取性能指标数据
    Generator->>DataService: 获取日志和事件数据
    Generator->>DataService: 获取告警数据
    
    Generator->>Generator: 数据分析与处理
    Generator->>Template: 应用报告模板
    Template->>Generator: 返回报告内容
    
    Generator->>Storage: 存储报告文件
    Generator->>TaskSession: 更新报告引用
    Generator->>Notifier: 触发报告通知
    Notifier->>Notifier: 执行分发规则
```

**报告格式与结构**：
1. **HTML格式**：
   - 交互式图表和数据可视化
   - 支持钻取查看详细数据
   - 响应式设计，适应不同设备
2. **PDF格式**：
   - 适合打印和归档
   - 包含静态图表和表格
   - 支持书签和目录
3. **Docx格式**：
   - 支持进一步编辑和修改
   - 兼容Microsoft Office
   - 包含可编辑的图表和表格

**报告内容结构**：
```
1. 执行摘要
   1.1 任务概述
   1.2 关键发现
   1.3 影响评级

2. 任务详情
   2.1 任务元数据
   2.2 执行时间线
   2.3 目标设备列表
   2.4 变更操作摘要

3. 性能影响分析
   3.1 CPU使用率分析
   3.2 内存使用率分析
   3.3 网络流量分析
   3.4 关键指标变化图表
   3.5 异常指标标记
   3.6 性能趋势分析

4. 日志分析
   4.1 关键事件时间线
   4.2 配置变更日志分析
   4.3 错误和警告日志摘要
   4.4 系统消息分析

5. 告警分析
   5.1 任务期间触发的告警列表
   5.2 告警统计和分类
   5.3 与历史同期告警对比

6. 网络事件关联分析
   6.1 配置变更与网络事件关联
   6.2 潜在因果关系分析
   6.3 拓扑影响范围

7. 总体评估
   7.1 变更影响评级
   7.2 潜在问题和建议
   7.3 后续监控建议

8. 附录
   8.1 详细数据表格
   8.2 原始日志摘录
   8.3 技术参考
```

**自定义模板功能**：
- 提供基于Go模板引擎的模板定制能力
- 支持组织级别的报告模板
- 支持按任务类型选择不同模板
- 允许用户自定义报告章节和内容
- 提供模板版本控制和管理

**报告分发机制**：
```sql
-- MySQL数据库表定义
CREATE TABLE report_distribution_rules (
    id VARCHAR(36) PRIMARY KEY COMMENT '规则唯一标识符',
    name VARCHAR(100) NOT NULL COMMENT '规则名称',
    task_type VARCHAR(50) COMMENT '适用任务类型',
    device_group VARCHAR(50) COMMENT '适用设备组',
    impact_level ENUM('none', 'low', 'medium', 'high', 'critical') COMMENT '适用影响级别',
    distribution_channels JSON NOT NULL COMMENT '分发渠道配置',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    updated_at DATETIME NOT NULL COMMENT '更新时间',
    INDEX idx_task_type (task_type),
    INDEX idx_impact_level (impact_level)
);

CREATE TABLE report_distributions (
    id VARCHAR(36) PRIMARY KEY COMMENT '分发记录唯一标识符',
    report_id VARCHAR(36) NOT NULL COMMENT '报告ID',
    rule_id VARCHAR(36) NOT NULL COMMENT '应用的规则ID',
    channel_type VARCHAR(50) NOT NULL COMMENT '分发渠道类型',
    recipient VARCHAR(255) NOT NULL COMMENT '接收者',
    status ENUM('pending', 'sent', 'failed', 'delivered', 'read') NOT NULL COMMENT '分发状态',
    sent_at DATETIME COMMENT '发送时间',
    error_message TEXT COMMENT '错误信息',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    updated_at DATETIME NOT NULL COMMENT '更新时间',
    FOREIGN KEY (report_id) REFERENCES task_reports(id),
    FOREIGN KEY (rule_id) REFERENCES report_distribution_rules(id),
    INDEX idx_report_channel (report_id, channel_type),
    INDEX idx_status (status)
);
```

**分发渠道支持**：
1. **电子邮件**：
   - 支持HTML和附件格式
   - 可配置收件人、抄送和密送
   - 支持邮件模板定制
2. **消息系统**：
   - 集成企业即时通讯平台
   - 支持报告摘要和链接发送
3. **共享存储**：
   - 自动保存到指定网络位置
   - 支持文件命名规则配置
4. **Webhook**：
   - 支持向第三方系统推送报告
   - 可配置推送格式和认证信息
5. **API接口**：
   - 提供报告查询API
   - 支持外部系统集成

## 3.2 数据模型

### 3.2.1 任务会话数据模型

任务会话是协同监控的核心数据实体，记录任务的基本信息和状态。

```sql
-- MySQL数据库表定义
CREATE TABLE task_sessions (
    id VARCHAR(36) PRIMARY KEY COMMENT '会话唯一标识符',
    task_id VARCHAR(36) NOT NULL COMMENT '自动化任务ID',
    status ENUM('pending', 'monitoring', 'completed', 'failed', 'aborted') NOT NULL COMMENT '会话状态',
    start_time DATETIME COMMENT '监控开始时间',
    end_time DATETIME COMMENT '监控结束时间',
    task_type VARCHAR(50) COMMENT '任务类型',
    target_devices JSON COMMENT '目标设备列表',
    metadata JSON COMMENT '任务元数据',
    report_id VARCHAR(36) COMMENT '关联报告ID',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    updated_at DATETIME NOT NULL COMMENT '更新时间',
    INDEX idx_task_id (task_id),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time)
);
```

### 3.2.2 任务数据集模型

任务数据集存储与任务相关的各类数据引用。

```sql
-- MySQL数据库表定义
CREATE TABLE task_datasets (
    id VARCHAR(36) PRIMARY KEY COMMENT '数据集唯一标识符',
    task_session_id VARCHAR(36) NOT NULL COMMENT '关联任务会话ID',
    data_type ENUM('metric', 'log', 'alert', 'event') NOT NULL COMMENT '数据类型',
    source_type VARCHAR(50) NOT NULL COMMENT '数据源类型',
    source_reference VARCHAR(255) NOT NULL COMMENT '数据源引用',
    time_range_start DATETIME NOT NULL COMMENT '时间范围开始',
    time_range_end DATETIME NOT NULL COMMENT '时间范围结束',
    metadata JSON COMMENT '数据集元数据',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    FOREIGN KEY (task_session_id) REFERENCES task_sessions(id),
    INDEX idx_task_session_data_type (task_session_id, data_type)
);
```

### 3.2.3 任务报告模型

任务报告存储任务执行影响评估报告的元数据和内容引用。

```sql
-- MySQL数据库表定义
CREATE TABLE task_reports (
    id VARCHAR(36) PRIMARY KEY COMMENT '报告唯一标识符',
    task_session_id VARCHAR(36) NOT NULL COMMENT '关联任务会话ID',
    report_type VARCHAR(20) NOT NULL COMMENT '报告类型',
    title VARCHAR(255) NOT NULL COMMENT '报告标题',
    content_path VARCHAR(255) NOT NULL COMMENT '报告内容路径',
    summary TEXT COMMENT '报告摘要',
    impact_level ENUM('none', 'low', 'medium', 'high', 'critical') COMMENT '影响级别',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    FOREIGN KEY (task_session_id) REFERENCES task_sessions(id),
    INDEX idx_task_session_id (task_session_id)
);
```

## 3.3 接口设计

### 3.3.1 任务信号接收接口

#### REST API

```
POST /api/v1/tasks/signals
```

**请求参数**：
```json
{
  "signal_type": "start|end|abort",
  "task_id": "uuid-string",
  "timestamp": "ISO8601-timestamp",
  "task_type": "configuration|upgrade|rollback",
  "target_devices": ["device1", "device2"],
  "expected_duration": 300,
  "status": "success|failed|aborted",
  "metadata": {
    "key1": "value1",
    "key2": "value2"
  }
}
```

**响应**：
```json
{
  "success": true,
  "session_id": "uuid-string",
  "message": "Task signal received"
}
```

#### Kafka消息格式

**Topic**: `dci.task.signals`

**消息格式**：与REST API请求体相同

### 3.3.2 任务会话管理接口

#### 创建任务会话

```
POST /api/v1/tasks/sessions
```

**请求参数**：
```json
{
  "task_id": "uuid-string",
  "task_type": "configuration|upgrade|rollback",
  "target_devices": ["device1", "device2"],
  "expected_duration": 300,
  "metadata": {
    "key1": "value1",
    "key2": "value2"
  }
}
```

**响应**：
```json
{
  "session_id": "uuid-string",
  "status": "pending",
  "created_at": "ISO8601-timestamp"
}
```

#### 更新任务会话状态

```
PUT /api/v1/tasks/sessions/{session_id}/status
```

**请求参数**：
```json
{
  "status": "monitoring|completed|failed|aborted",
  "end_time": "ISO8601-timestamp",
  "result_status": "success|failed|aborted"
}
```

**响应**：
```json
{
  "session_id": "uuid-string",
  "status": "updated-status",
  "updated_at": "ISO8601-timestamp"
}
```

#### 查询任务会话

```
GET /api/v1/tasks/sessions/{session_id}
```

**响应**：
```json
{
  "id": "session-uuid",
  "task_id": "task-uuid",
  "status": "monitoring",
  "start_time": "ISO8601-timestamp",
  "end_time": "ISO8601-timestamp",
  "task_type": "configuration",
  "target_devices": ["device1", "device2"],
  "metadata": {
    "key1": "value1"
  },
  "report_id": "report-uuid",
  "created_at": "ISO8601-timestamp",
  "updated_at": "ISO8601-timestamp"
}
```

### 3.3.3 报告生成接口

#### 请求生成报告

```
POST /api/v1/tasks/sessions/{session_id}/reports
```

**请求参数**：
```json
{
  "report_type": "html|pdf",
  "title": "任务执行影响报告",
  "include_sections": ["summary", "metrics", "logs", "alerts", "assessment"]
}
```

**响应**：
```json
{
  "report_id": "uuid-string",
  "status": "generating",
  "estimated_completion": "ISO8601-timestamp"
}
```

#### 查询报告状态

```
GET /api/v1/tasks/reports/{report_id}
```

**响应**：
```json
{
  "id": "report-uuid",
  "task_session_id": "session-uuid",
  "status": "completed",
  "report_type": "html",
  "title": "任务执行影响报告",
  "content_url": "/reports/html/report-uuid.html",
  "summary": "任务执行成功，对网络性能影响较小",
  "impact_level": "low",
  "created_at": "ISO8601-timestamp",
  "completed_at": "ISO8601-timestamp"
}
```

# 4 安全设计

## 4.1 身份认证与授权

- 使用JWT令牌进行API身份认证
- 基于角色的访问控制，限制任务信号接收和报告访问权限
- 网络自动化控制系统与数据监测系统之间的通信使用双向TLS验证

## 4.2 数据安全

- 敏感配置信息在存储和传输过程中进行加密
- 任务报告访问需要授权验证
- 数据库访问使用最小权限原则
- 定期审计日志，监控异常访问行为

# 5 测试方案

## 5.1 功能测试范围

1. 任务信号接收功能测试
   - 正常任务开始信号处理
   - 正常任务结束信号处理
   - 异常任务中止信号处理
   - 无效信号处理

2. 任务会话管理功能测试
   - 会话创建和状态更新
   - 会话查询和列表获取
   - 异常处理和错误恢复

3. 数据收集功能测试
   - 指标数据收集准确性
   - 日志数据收集完整性
   - 告警关联正确性

4. 报告生成功能测试
   - 报告内容完整性
   - 报告格式正确性
   - 大规模数据报告生成性能
