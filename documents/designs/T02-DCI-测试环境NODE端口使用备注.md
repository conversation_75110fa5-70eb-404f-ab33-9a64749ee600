# DCI 测试环境端口使用备注

> 本文档根据阿里云SLB（负载均衡）控制台截图和Kubernetes集群`kubectl get svc -ndci`命令输出进行综合整理，旨在提供一份准确、完整的端口使用情况说明。
> 
> - **SLB 公网地址**: `10.247.20.250` 

## 端口映射详情

| 应用/服务名 (K8S Service) | 协议 | 服务内部端口 | K8S NodePort | SLB 暴露端口 | SLB 监听状态 | 用途/备注 |
| :--- | :-- | :--- | :--- | :--- | :--- | :--- |
| **dcimonitor-service** | TCP | 8080 | **30000** | **30000** | 运行中 / 正常 | DCI Monitor 后端API服务 |
| **prometheus-nodeport** | TCP | 9090 | **30006** | **30006** | 运行中 / 正常 | Prometheus UI及API访问 |
| **kafka** | TCP | 9092 | **30002** | **30002** | 运行中 / 正常 | Kafka Broker主端口 (SASL/PLAIN) |
| **kafka-0-external** | TCP | 30010 | **30010** | **30010** | 运行中 / 正常 | Kafka Broker-0 独立外部监听 |
| **kafka-1-external** | TCP | 30011 | **30011** | **30011** | 运行中 / 正常 | Kafka Broker-1 独立外部监听 |
| **kafka-2-external** | TCP | 30012 | **30012** | **30012** | 运行中 / 正常 | Kafka Broker-2 独立外部监听 |
| **logstash** | UDP | 5140 | **30005** | **30005** | 运行中 / 正常 | Syslog 日志接收 |
| **dcimonitor-flowdata-service** | TCP | 9090 | **30007** | - | **未通过SLB暴露** | flowdata指标端点 (由Prometheus在内网抓取) |
| **logstash** | TCP | 5044 | 31001 | - | 未通过SLB暴露 | Beats 日志接收 |
| **logstash** | TCP | 9600 | 31002 | - | 未通过SLB暴露 | Logstash Web API |
| **logstash** | TCP | 8080 | 31012 | - | 未通过SLB暴露 | (备用) |
| (ingress) | TCP | - | - | **8000** | 运行中 / 正常 | Ingress Controller 暴露的服务 |
| (ingress) | TCP | - | - | **80** | 运行中 / 正常 | Ingress Controller (HTTP) |
| (ingress) | TCP | - | - | **443** | 运行中 / 正常 | Ingress Controller (HTTPS) |
| (ingress) | TCP | - | - | **6379** | 运行中 / 正常 | Redis 服务 (通过Ingress暴露) |
| (已停用) | TCP | - | - | **30001** | **已停止 / 异常** | 原 Kibana 端口, 现已停用 |
| (后端异常) | TCP | - | - | **30004** | **运行中 / 异常** | 原 Taosd HTTP 端口, 后端健康检查失败 |
