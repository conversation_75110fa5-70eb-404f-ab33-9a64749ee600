


1.  **提前引入核心业务功能：** 将自动化任务监控、报告生成等核心功能更早地纳入迭代计划，而不仅仅是放在后期。
2.  **明确组件职责：** 在任务中更清晰地指明需要开发或配置的服务端组件（如 `TaskSignalReceiver`, `ReportGenSvc`, 各 Query Service 等）。
3.  **细化基础设施和数据模型准备：** 在早期阶段就明确所有需要的 Kafka Topic 和 MySQL 表结构，避免后期频繁变更。
4.  **整合数据源接入：** 将 SNMP、Syslog、sFlow/NetFlow 等关键数据源的接入更明确地安排进开发阶段。
5.  **规划租户数据供应【暂不涉及】：** 将作为核心需求的租户数据服务 API 单独列为一个阶段。
6.  **拆分后期阶段【暂不涉及】：** 将原先笼统的 "Phase 6+" 拆分为更具体的阶段，如租户功能、高级特性等。

---

**优化后的分期迭代开发计划**

**核心原则:** 

*   **MVP优先:** 先构建最核心的采集、传输、存储和远程管理骨干。
*   **增量构建:** 每个阶段在前一阶段的基础上增加功能。
*   **价值驱动:** 每个阶段都应产生可测试或可演示的成果。
*   **并行可能:** 部分后端的查询、可视化工作可以在客户端管理功能完善的同时进行。

---

**Phase 0【当前阶段】: 基础设施搭建与核心 Schema 定义 (目标：环境就绪)**

*   **目标:** 部署所有必需的基础设施，并定义核心的数据模型（数据库表、Kafka Topic）。
*   **主要任务:**
    1.  **部署基础设施（在 K8S 上部署）:**
        *   部署 Kafka 集群。 -- 进行中
        *   部署 TDengine 集群。 -- 完成
        *   部署 Elasticsearch 集群。 -- 完成
        *   部署 MySQL 服务器。 -- 进行中
        *   部署 Logstash (基础配置)。 -- 完成
        *   部署 taosAdapter (基础配置)。 -- 完成
        *   部署 prometheus 但实例。 -- 完成
    2.  **定义并创建 Kafka Topic:**
        *   创建 `metrics`, `logs_events`, `flows` 三个核心 Topic。
    3.  **定义并初始化核心 MySQL Schema (`dcimonitor_mgmt` 数据库):**
        *   创建 `ClientRegistry` 表 (客户端注册与状态)。
        *   创建 `ConfigStore` 表 (Telegraf 配置存储，含版本)。
        *   创建 `TaskMetadataDB` 表 (自动化任务元数据)。
        *   创建 `TenantMapDB` 表 (租户资源映射)。
        *   创建 `ReportMetadataDB` 表 (报告元数据)。
        *   *说明:* 此阶段仅定义表结构，业务逻辑后续填充。
    4.  **初始化 TDengine/Elasticsearch:**
        *   在 TDengine 中创建数据库 (`dci_metrics`) 和几个核心业务相关的超级表示例 (e.g., `st_interface_counters`, `st_device_health`)。
        *   在 Elasticsearch 中配置基础索引模板 (推荐基于 ECS)。
    5.  **基础管道连通性测试:**
        *   手动配置 Telegraf (含 `inputs.cpu`, `outputs.kafka`)，验证 Metrics -> Kafka -> taosAdapter -> TDengine 管道。
        *   手动配置 Telegraf (含 `inputs.syslog`, `outputs.kafka`)，验证 Logs -> Kafka -> Logstash -> Elasticsearch 管道。
*   **可交付成果:**
    *   所有后端基础设施运行正常，核心 Topic 和 DB Schema 已创建。
    *   手动测试证明数据可以从 Telegraf 流入 TDengine 和 Elasticsearch。
*   **意义:** 搭建了完整的运行环境和数据模型基础，为后续开发铺平道路。

**Phase 1: 客户端远程管理 - 注册、心跳与状态上报 (目标：感知与基础监控)**

*   **目标:** 实现 Agent 的基本生命周期管理和状态上报，让服务端能感知客户端及其基础状态。
*   **主要任务:**
    1.  **服务端 (`dcimonitor` - RemoteManagement 模块):**
        *   开发 `MgmtAPI` 的 `/register` 端点逻辑 (写入 `ClientRegistry`)。
        *   开发 `MgmtAPI` 的 `/heartbeat` 端点逻辑 (更新 `ClientRegistry` 中的心跳时间、Agent 状态、*以及本地 Telegraf 进程运行状态*)。
    2.  **服务端 (`dcimonitor` - CoreServices 模块):**
        *   开发基础内部 API (`/api/v1/internal/clients`) 用于查询 `ClientRegistry` 获取客户端列表和状态。
    3.  **客户端 (`Management Agent`):**
        *   开发 Agent 启动时的自动注册逻辑。
        *   开发 Agent 定期发送心跳，包含自身状态和**探测到的 Telegraf 进程状态**。
        *   实现 Agent 基本的日志记录。
*   **可交付成果:**
    *   Agent 能成功注册并定期上报心跳及 Telegraf 状态。
    *   服务端可通过内部 API 获取客户端列表和在线状态。
*   **意义:** 建立了远程管理的基础连接和状态监控。

**Phase 2: 远程配置下发与核心数据采集 (目标：自动化基础数据流)**

*   **目标:** 实现服务端下发基础 Telegraf 配置，驱动 Agent 应用，并开始采集核心 DCI 指标和日志。
*   **主要任务:**
    1.  **服务端 (`dcimonitor` - RemoteManagement 模块):**
        *   开发 `MgmtAPI` 的 `/config` (GET) 端点，供 Agent 拉取其对应的最新配置版本 (基于 `ConfigStore`)。
        *   提供方式（脚本/API）向 `ConfigStore` 存入一个包含核心输入的默认 Telegraf 配置（版本 1）。
            *   **核心输入:** `inputs.snmp` (采集接口计数器 `ifInOctets/ifOutOctets/Errors/Discards`, CPU, Mem), `inputs.syslog`。
            *   **核心输出:** `outputs.kafka`，配置好将不同输入（Metrics, Logs）路由到对应 Topic 的逻辑（可用 Tag）。
    2.  **服务端 (`dcimonitor` - CoreServices 模块):**
        *   开发基础的 `TSDB_Query` 和 `ES_Query` 服务（封装数据库连接和简单查询代理），供后续 API/UI/告警使用。
    3.  **客户端 (`Management Agent`):**
        *   开发 Agent 定期轮询 `/config` 拉取配置（对比版本号）。
        *   开发 Agent 应用配置（写入 `telegraf.d/managed.conf`）并通知 Telegraf 重载 (`SIGHUP` 或 reload)。
        *   增强 Agent 心跳，上报当前应用的配置版本。
    4.  **数据验证:** 验证 SNMP 指标和 Syslog 数据自动流入 TDengine 和 Elasticsearch，结构正确。
*   **可交付成果:**
    *   Agent 能自动拉取并应用服务端配置，开始采集 SNMP 指标和 Syslog。
    *   核心监控数据自动、持续地流入后端数据库。
    *   基础查询服务可用。
*   **意义:** 打通了自动化的核心数据采集管道，为后续分析和可视化奠定基础。

**Phase 3: 动态配置、基础 UI 与远程命令 (目标：灵活管控与交互)**

*   **目标:** 实现通过 UI 动态管理客户端配置，并能远程控制 Telegraf 进程。
*   **主要任务:**
    1.  **服务端 (`dcimonitor` - RemoteManagement 模块):**
        *   增强 `MgmtAPI` 的 `/config` (PUT) 端点，允许更新指定客户端/组的配置（写入 `ConfigStore`，更新版本）。
        *   开发 `MgmtAPI` 的 `/commands` (POST/GET) 端点，用于接收 UI 命令并让 Agent 拉取待执行命令 (Start/Stop/Restart)。
        *   在 `ClientRegistry` 或单独机制中记录命令状态。
    2.  **服务端 (`dcimonitor` - CoreServices 模块):**
        *   开发 `UI_Backend`，提供面向前端的 API：
            *   获取客户端列表及状态 (调用 `/api/v1/internal/clients`)。
            *   获取/更新客户端配置 (调用 `MgmtAPI /config`)。
            *   发送远程命令 (调用 `MgmtAPI /commands`)。
    3.  **UI (`dcimonitor` 前端):**
        *   开发客户端列表页面，显示状态、配置版本。
        *   开发配置编辑页面 (如 CodeMirror 或 Textarea)，允许查看、编辑、保存 Telegraf 配置。
        *   在客户端列表或详情页添加 Start/Stop/Restart 按钮。
    4.  **客户端 (`Management Agent`):**
        *   增强 Agent 拉取配置逻辑，处理版本更新。
        *   开发 Agent 轮询 `/commands` 获取命令并执行相应系统调用 (`systemctl start/stop/restart telegraf`)。
        *   增强 Agent 心跳，上报命令执行结果和最新的 Telegraf 状态。
*   **可交付成果:**
    *   可通过 UI 查看客户端、动态编辑/下发配置、远程启停 Telegraf。
    *   实现了远程管理的闭环控制。
*   **意义:** 大大增强了系统的可管理性和灵活性。

**Phase 4: 自动化任务监控与初步报告 (目标：业务场景联动)**

*   **目标:** 实现对网络自动化任务启停信号的接收和基本监控，并生成初步的任务报告。
*   **主要任务:**
    1.  **服务端 (`dcimonitor` - RemoteManagement 模块):**
        *   开发 `TaskSignalReceiver` 服务：
            *   提供 REST API 接口接收网络自动化控制系统的任务启动、结束、异常中断和回滚信号。
            *   解析任务信号（包含 TaskID, 关联设备等）。
            *   在 `TaskMetadataDB` 中创建/更新任务记录（状态：Running/Ended/Rolled Back等）。
    2.  **服务端 (`dcimonitor` - CoreServices 模块):**
        *   开发 `TaskMonitorSvc` 服务：
            *   监控 `TaskMetadataDB` 中任务状态变化。
            *   当任务状态变为 "Ended" 时，触发 `ReportGenSvc`。
        *   开发基础 `ReportGenSvc` 服务：
            *   接收 TaskID，查询 `TaskMetadataDB` 获取任务上下文（时间范围、设备）。
            *   调用 `TSDB_Query`/`ES_Query` 查询任务期间相关设备的基础指标/日志**概要**（例如，日志错误数统计、接口速率峰值）。
            *   生成**简单文本格式**的任务报告（总结任务信息和概要数据）。
            *   将报告内容存储（初期可简单存入 `ReportMetadataDB` 的 TEXT 字段或本地文件）。
            *   更新 `ReportMetadataDB` 记录报告元数据，并更新 `TaskMetadataDB` 中任务的报告关联信息。
        *   开发内部 API (`/api/v1/internal/tasks`, `/api/v1/internal/reports`) 查询任务和报告列表。
    3.  **UI (`dcimonitor` 前端):**
        *   添加页面显示任务列表 (来自 `/api/v1/internal/tasks`)。
        *   添加页面显示报告列表 (来自 `/api/v1/internal/reports`)，并能查看简单的文本报告内容。
    4.  **测试:** 通过 REST API 客户端工具（如Postman）手动发送任务启停信号，验证整个流程。
*   **可交付成果:**
    *   系统能接收外部任务信号，记录任务状态。
    *   任务结束后能自动触发生成一个包含基本监控摘要的文本报告。
    *   UI 可查看任务和报告列表。
*   **意义:** 实现了与核心业务场景（自动化任务）的初步联动和价值输出。

**Phase 5: 可视化、告警与 sFlow/NetFlow 集成 (目标：全面洞察与主动发现)**

*   **目标:** 实现监控数据的全面可视化，集成告警，并接入重要的流数据源。
*   **主要任务:**
    1.  **可视化 (Grafana):**
        *   部署 Grafana。
        *   配置 TDengine, Elasticsearch, MySQL 数据源。
        *   创建核心业务仪表盘：
            *   DCI 概览 (客户端状态、任务概览、告警概览)。
            *   设备性能详情 (CPU/Mem/接口流量/错误/丢包 - 基于 SNMP)。
            *   Syslog 分析仪表盘 (错误分布、趋势)。
            *   **sFlow/NetFlow 流量分析仪表盘** (Top Talkers, Top Protocols - 需要 ES 数据)。
            *   自动化任务监控视图 (关联任务的指标/日志变化)。
    2.  **告警:**
        *   配置 Grafana Alerting 或选用内部告警引擎方案。
        *   配置关键告警规则：客户端心跳丢失、设备指标阈值、关键 Syslog 错误、**接口错误/丢包率异常**、**自动化任务异常** (例如，长时间未结束)。
        *   配置通知渠道。
    3.  **sFlow/NetFlow 集成:**
        *   更新 Telegraf 配置 (`inputs.sflow` 或 `inputs.netflow`) 并通过远程管理下发。
        *   配置 Logstash/Fluentd 解析 sFlow/NetFlow 数据，丰富字段，发送到 Elasticsearch 的 `flows` 索引。
    4.  **服务端 (`dcimonitor` API):** 确保有 API 支持 Grafana 查询（特别是 MySQL 中的状态数据）和可能的内部告警引擎需求。
*   **可交付成果:**
    *   丰富的 Grafana 仪表盘提供多维度监控视图。
    *   核心业务异常可触发告警通知。
    *   sFlow/NetFlow 数据被采集、处理并可视化。
*   **意义:** 显著提升了系统的可观测性和主动发现问题的能力。

**Phase 6: 租户数据供应与报告增强 (目标：服务化与深度分析)**

*   **目标:** 实现面向租户的数据服务 API，并增强报告生成能力。
*   **主要任务:**
    1.  **租户数据供应:**
        *   实现 `TenantMapDB` (MySQL) 的管理机制（API 或后台脚本录入租户与资源映射）。
        *   **模拟/集成 TenantIDP:** 实现基于 Token 的认证（初期可 mock，后期对接真实 IDP）。
        *   开发 `API_Orig` (业务/数据供应 API) 的租户接口 (`/api/v1/tenant/...`)：
            *   验证 Token 并解析 TenantID。
            *   查询 `TenantMapDB` 获取权限。
            *   **重写**租户查询请求（强制加入资源过滤）。
            *   代理查询到 `TSDB_Query`/`ES_Query`。
            *   返回过滤后的结果。
    2.  **报告增强:**
        *   增强 `ReportGenSvc`：
            *   实现更复杂的数据查询和分析逻辑（例如，自动化任务前后对比、流量趋势分析）。
            *   支持生成更丰富的报告格式（如 PDF、HTML，可能需要引入模板引擎和渲染库）。
            *   实现将报告存储到外部存储 (S3/NAS) 的逻辑。
        *   实现**周期性报告**（日报、周报）的触发机制（如 CronJob 调用 API）。
    3.  **UI (`dcimonitor` 前端):**
        *   (可选) 提供租户自助查询接口文档或简单界面。
        *   增强报告页面，支持下载不同格式的报告。
*   **可交付成果:**
    *   授权租户可通过 API 安全地访问其相关的监控数据。
    *   系统能生成内容更丰富、格式更多样的自动化任务报告和周期性报告。
*   **意义:** 实现了系统对外服务化的核心能力，并深化了数据分析价值。

**Phase 7: 高级功能、优化与加固 (目标：成熟与健壮)**

*   **目标:** 提升系统易用性、性能、稳定性、安全性，并按需集成更高级的数据源和功能。
*   **主要任务 (根据优先级选择):**
    *   **配置模板化:** 服务端支持 Telegraf 配置模板。
    *   **Telemetry 集成:** 接入流式遥测数据。
    *   **BGP-LS/BMP 集成:** 接入 BGP 数据，用于拓扑或路由分析 (若需求明确)。
    *   **拓扑可视化:** 开发或集成网络拓扑展示。
    *   **安全性增强:** mTLS、更细粒度权限、依赖库扫描等。
    *   **Agent 健壮性:** 自愈、资源限制。
    *   **性能优化:** 数据库调优、API 缓存、高并发处理。
    *   **高可用:** `dcimonitor` 服务、MySQL、Agent 等组件的高可用部署。
    *   **高级告警:** 关联分析、异常检测、告警抑制。
    *   **自动化测试:** 完善端到端测试覆盖。
    *   **文档完善:** 运维手册、API 文档。
*   **可交付成果:** 一个功能完善、性能优越、安全可靠的企业级监控平台。

---

**跨阶段注意事项:** (保持不变)

*   **测试:** 每个阶段都需要进行充分的单元测试、集成测试和手动/场景测试。
*   **文档:** 持续更新架构、API、运维和用户文档。
*   **代码审查:** 保证代码质量和一致性。
*   **CI/CD:** 建立并维护持续集成和部署流程。

这个优化后的计划将核心业务功能（如自动化任务监控和报告）提前，更紧密地结合了设计文档中的组件和流程，并为租户服务等关键需求规划了专门的阶段，希望能更好地指导项目的迭代开发。
