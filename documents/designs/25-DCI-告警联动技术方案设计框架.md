---
title: |
  DCI告警联动技术方案设计框架

subtitle: |
  告警系统与其他业务系统协同联动技术方案
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-7-27 | 顾铠羟 | 初始版本           |

# 1 概述

## 1.1 设计背景

DCI数据监测系统作为整体解决方案中的监控子系统，需要与多云管理系统、网络自动化控制系统等协同工作。当前`monitor_alert`表设计主要专注于告警本身的管理，缺乏与其他系统联动的必要字段，无法支撑系统间的协同工作。

## 1.2 设计目标

* 建立告警系统与其他业务系统的联动机制
* 实现告警与网络自动化任务的关联分析
* 支持告警与配置变更的因果关系分析
* 提供告警与网络控制操作的联动处理
* 构建统一的告警联动数据模型

## 1.3 设计原则

* **数据一致性**：确保联动数据在多个系统间的一致性
* **可追溯性**：支持告警事件的完整追溯链路
* **可扩展性**：支持未来新增联动场景的扩展
* **性能优化**：避免联动查询对系统性能造成显著影响

# 2 告警联动整体架构

## 2.1 系统联动关系

```mermaid
graph TB
    subgraph "告警系统"
        A[monitor_alert表]
        B[告警规则引擎]
        C[告警处理服务]
    end
    
    subgraph "网络自动化系统"
        D[任务管理]
        E[配置变更]
        F[执行引擎]
    end
    
    subgraph "网络控制系统"
        G[网络控制]
        H[自动化操作]
        I[修复任务]
    end
    
    subgraph "多云管理系统"
        J[拓扑管理]
        K[设备管理]
        L[配置管理]
    end
    
    A -. "task_session_id" .-> D
    A -. "config_change_id" .-> E
    A -. "automation_action_id" .-> H
    A -. "device_id" .-> K
    A -. "topology_change_id" .-> J
```

## 2.2 联动数据流

```mermaid
sequenceDiagram
    participant Alert as 告警系统
    participant Task as 网络自动化系统
    participant Control as 网络控制系统
    participant Monitor as 数据监测系统
    
    Task->>Monitor: 发送任务启动信号
    Monitor->>Alert: 创建监控会话
    Monitor->>Monitor: 收集任务期间数据
    Monitor->>Alert: 生成关联告警
    Alert->>Control: 触发自动化修复
    Control->>Alert: 更新告警状态
    Task->>Monitor: 发送任务结束信号
    Monitor->>Alert: 生成任务分析报告
```

# 3 联动字段扩展方案

## 3.1 monitor_alert表字段扩展

### 3.1.1 网络自动化任务联动字段

* **task_session_id** (VARCHAR(64))：关联网络自动化任务会话ID
* **task_phase** (ENUM)：告警发生的任务阶段（prep/exec/post）
* **task_correlation_type** (ENUM)：与任务的关联类型（caused_by/triggered_during/resolved_by）
* **task_start_time** (TIMESTAMP)：关联任务开始时间
* **task_end_time** (TIMESTAMP)：关联任务结束时间

### 3.1.2 配置变更联动字段

* **config_change_id** (VARCHAR(64))：关联具体的配置变更记录
* **change_correlation_score** (DECIMAL(3,2))：与配置变更的相关性评分
* **change_impact_scope** (JSON)：配置变更的影响范围
* **change_rollback_status** (ENUM)：回滚状态（none/attempted/successful/failed）

### 3.1.3 网络控制联动字段

* **automation_action_id** (VARCHAR(64))：关联的自动化操作ID
* **remediation_task_id** (VARCHAR(64))：关联的修复任务ID
* **escalation_policy_id** (VARCHAR(64))：关联的升级策略ID
* **control_response_time** (INT)：控制响应时间（毫秒）

### 3.1.4 因果关系分析字段

* **correlation_group_id** (VARCHAR(64))：关联告警组ID
* **root_cause_alert_id** (VARCHAR(64))：根因告警ID
* **trigger_context** (JSON)：触发上下文信息
* **impact_scope** (JSON)：影响范围定义

## 3.2 联动索引设计

```sql
-- 任务联动索引
CREATE INDEX idx_alert_task_session ON monitor_alert(task_session_id);
CREATE INDEX idx_alert_task_phase ON monitor_alert(task_phase, task_session_id);

-- 配置变更联动索引
CREATE INDEX idx_alert_config_change ON monitor_alert(config_change_id);
CREATE INDEX idx_alert_correlation_score ON monitor_alert(change_correlation_score DESC);

-- 网络控制联动索引
CREATE INDEX idx_alert_automation_action ON monitor_alert(automation_action_id);
CREATE INDEX idx_alert_remediation_task ON monitor_alert(remediation_task_id);

-- 因果关系索引
CREATE INDEX idx_alert_correlation_group ON monitor_alert(correlation_group_id);
CREATE INDEX idx_alert_root_cause ON monitor_alert(root_cause_alert_id);
```

# 4 联动机制设计

## 4.1 网络自动化任务协同监控联动

### 4.1.1 联动触发机制

* **任务启动联动**：接收网络自动化控制系统下发的任务启动信号，创建监控会话
* **告警关联**：任务期间触发的告警自动关联到当前任务会话
* **影响评估**：基于任务期间的告警情况评估配置变更影响
* **任务结束联动**：任务结束后生成分析报告，更新告警状态

### 4.1.2 数据关联策略

* **时间窗口关联**：任务执行时间窗口内的告警自动关联
* **设备范围关联**：任务影响设备范围内的告警自动关联
* **因果关系关联**：基于告警内容与任务变更内容的语义关联

## 4.2 配置变更联动机制

### 4.2.1 变更影响分析

* **相关性评分**：基于告警内容与配置变更内容的相似度计算相关性
* **时间相关性**：分析告警发生时间与配置变更时间的关联性
* **设备相关性**：分析告警设备与配置变更设备的关联性

### 4.2.2 回滚联动处理

* **回滚触发**：配置变更回滚时自动更新相关告警状态
* **影响评估**：回滚后重新评估告警的根因分析
* **状态同步**：确保告警状态与配置变更状态的一致性

## 4.3 网络控制联动机制

### 4.3.1 自动化修复联动

* **修复触发**：特定告警自动触发网络控制系统的修复操作
* **修复跟踪**：跟踪修复操作的执行状态和结果
* **告警更新**：修复完成后自动更新告警状态

### 4.3.2 升级策略联动

* **升级触发**：告警级别达到阈值时触发升级策略
* **策略执行**：执行预定义的升级处理流程
* **结果反馈**：升级策略执行结果反馈到告警系统

# 5 联动API设计

## 5.1 联动数据查询API

### 5.1.1 任务关联告警查询

```go
// GET /api/v1/alerts/task/{task_session_id}
type TaskAlertsResponse struct {
    Code int `json:"code"`
    Data struct {
        TaskInfo    TaskInfo     `json:"task_info"`
        Alerts      []AlertInfo  `json:"alerts"`
        Statistics  AlertStats   `json:"statistics"`
    } `json:"data"`
}
```

### 5.1.2 配置变更关联告警查询

```go
// GET /api/v1/alerts/config-change/{config_change_id}
type ConfigChangeAlertsResponse struct {
    Code int `json:"code"`
    Data struct {
        ChangeInfo  ConfigChangeInfo `json:"change_info"`
        Alerts      []AlertInfo      `json:"alerts"`
        Correlation float64           `json:"correlation_score"`
    } `json:"data"`
}
```

## 5.2 联动状态更新API

### 5.2.1 任务状态同步

```go
// POST /api/v1/alerts/task/{task_session_id}/sync
type TaskSyncRequest struct {
    Status      string    `json:"status" binding:"required,oneof=running completed failed"`
    EndTime     time.Time `json:"end_time"`
    Summary     string    `json:"summary"`
}
```

### 5.2.2 修复状态更新

```go
// POST /api/v1/alerts/{alert_id}/remediation
type RemediationUpdateRequest struct {
    ActionID    string `json:"action_id" binding:"required"`
    Status      string `json:"status" binding:"required,oneof=started completed failed"`
    Result      string `json:"result"`
}
```

# 6 联动数据处理流程

## 6.1 联动数据同步流程

```mermaid
sequenceDiagram
    participant External as 外部系统
    participant API as 告警API
    participant Service as 告警服务
    participant DAO as 数据访问层
    participant DB as 数据库
    
    External->>API: 发送联动数据
    API->>Service: 处理联动请求
    Service->>DAO: 查询相关告警
    DAO->>DB: 执行数据库操作
    DB-->>DAO: 返回查询结果
    DAO-->>Service: 返回告警数据
    Service->>Service: 更新联动字段
    Service->>DAO: 保存更新结果
    DAO->>DB: 执行更新操作
    DB-->>DAO: 确认更新
    DAO-->>Service: 返回更新结果
    Service-->>API: 返回处理结果
    API-->>External: 返回响应
```

## 6.2 联动数据一致性保障

* **事务处理**：联动数据更新使用数据库事务确保一致性
* **幂等性**：联动API设计为幂等操作，支持重复调用
* **状态同步**：定期同步外部系统状态，确保数据一致性
* **异常处理**：联动失败时提供重试机制和回滚策略

# 7 性能优化策略

## 7.1 查询性能优化

* **索引优化**：为联动字段建立合适的数据库索引
* **查询缓存**：对频繁查询的联动数据进行缓存
* **分页查询**：大量联动数据查询使用分页机制
* **异步处理**：非关键联动操作使用异步处理

## 7.2 数据存储优化

* **字段压缩**：JSON字段使用压缩存储减少空间占用
* **分区策略**：按时间分区提高查询性能
* **归档策略**：历史联动数据定期归档
* **清理策略**：无效联动数据定期清理

# 8 监控与运维

## 8.1 联动状态监控

* **联动成功率**：监控联动操作的执行成功率
* **响应时间**：监控联动API的响应时间
* **数据一致性**：监控联动数据的一致性状态
* **错误率**：监控联动操作的错误率

## 8.2 告警联动运维

* **联动日志**：记录所有联动操作的详细日志
* **故障排查**：提供联动故障的排查工具
* **数据修复**：提供联动数据不一致的修复工具
* **性能调优**：提供联动性能的调优建议

# 9 扩展性设计

## 9.1 新增联动场景

* **预留扩展字段**：在表结构中预留扩展字段
* **插件化架构**：支持新增联动场景的插件化扩展
* **配置化联动**：通过配置文件支持新增联动规则
* **API扩展**：预留API接口支持新增联动操作

## 9.2 联动规则引擎

* **规则配置**：支持通过配置定义联动规则
* **规则版本管理**：支持联动规则的版本管理
* **规则测试**：提供联动规则的测试环境
* **规则监控**：监控联动规则的执行效果

# 10 总结

本技术方案设计框架为DCI告警系统提供了完整的联动机制，通过扩展`monitor_alert`表字段、设计联动API、建立数据处理流程，实现了告警系统与网络自动化系统、网络控制系统等业务系统的协同工作。该框架具有良好的扩展性和可维护性，能够支撑未来新增的联动场景需求。
