---
title: |
  DCI网络控制-prometheus指标类告警联动技术方案设计

subtitle: |
  网络控制系统与prometheus指标告警协同联动技术方案
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-01-27 | 顾铠羟 | 初始版本           |

# 1 概述

## 1.1 设计背景

DCI数据监测系统通过Prometheus收集网络设备的指标数据，当指标超过预设阈值时触发告警。网络控制系统需要根据这些告警信息自动执行修复操作，实现告警与网络控制的联动处理。

## 1.2 设计目标

* 建立prometheus指标告警与网络控制系统的联动机制
* 实现基于告警级别的自动化修复策略
* 提供告警触发网络控制操作的完整流程
* 支持修复操作的执行状态跟踪和结果反馈
* 构建告警与网络控制的数据关联模型

## 1.3 设计原则

* **实时性**：确保告警触发网络控制操作的实时性
* **可靠性**：保证联动操作的可靠执行和状态同步
* **可追溯性**：完整记录联动操作的执行过程和结果
* **安全性**：确保网络控制操作的安全性和权限控制

# 2 联动架构设计

## 2.1 系统组件关系

```mermaid
graph TB
    subgraph "告警系统"
        A[Prometheus告警规则]
        B[AlertManager]
        C[monitor_alert表]
        D[告警处理服务]
    end
    
    subgraph "网络控制系统"
        E[网络控制API]
        F[自动化操作引擎]
        G[修复任务管理]
        H[策略执行器]
    end
    
    subgraph "网络设备"
        I[交换机]
        J[路由器]
        K[防火墙]
    end
    
    A -->|指标告警| B
    B -->|告警通知| C
    C -->|触发修复| D
    D -->|控制请求| E
    E -->|执行操作| F
    F -->|设备控制| G
    G -->|配置下发| H
    H -->|设备操作| I
    H -->|设备操作| J
    H -->|设备操作| K
```

## 2.2 联动数据流

```mermaid
sequenceDiagram
    participant Prometheus as Prometheus
    participant AlertManager as AlertManager
    participant AlertService as 告警服务
    participant ControlAPI as 网络控制API
    participant Device as 网络设备
    
    Prometheus->>AlertManager: 指标告警
    AlertManager->>AlertService: 告警通知
    AlertService->>AlertService: 评估告警级别
    AlertService->>ControlAPI: 触发修复操作
    ControlAPI->>ControlAPI: 验证操作权限
    ControlAPI->>Device: 执行修复操作
    Device-->>ControlAPI: 操作结果
    ControlAPI-->>AlertService: 更新告警状态
    AlertService->>AlertService: 记录联动日志
```

# 3 联动字段设计

## 3.1 monitor_alert表联动字段

### 3.1.1 网络控制联动字段

```sql
-- 网络控制联动字段
ALTER TABLE monitor_alert ADD COLUMN automation_action_id VARCHAR(64) COMMENT '关联的自动化操作ID';
ALTER TABLE monitor_alert ADD COLUMN remediation_task_id VARCHAR(64) COMMENT '关联的修复任务ID';
ALTER TABLE monitor_alert ADD COLUMN escalation_policy_id VARCHAR(64) COMMENT '关联的升级策略ID';
ALTER TABLE monitor_alert ADD COLUMN control_response_time INT COMMENT '控制响应时间(毫秒)';
ALTER TABLE monitor_alert ADD COLUMN control_status ENUM('pending', 'executing', 'completed', 'failed') DEFAULT 'pending' COMMENT '控制操作状态';
ALTER TABLE monitor_alert ADD COLUMN control_result JSON COMMENT '控制操作结果';
ALTER TABLE monitor_alert ADD COLUMN control_error_message TEXT COMMENT '控制操作错误信息';
```

### 3.1.2 联动索引设计

```sql
-- 网络控制联动索引
CREATE INDEX idx_alert_automation_action ON monitor_alert(automation_action_id);
CREATE INDEX idx_alert_remediation_task ON monitor_alert(remediation_task_id);
CREATE INDEX idx_alert_control_status ON monitor_alert(control_status);
CREATE INDEX idx_alert_level_control ON monitor_alert(level, control_status);
```

## 3.2 联动数据结构

### 3.2.1 告警联动请求结构

```go
// 告警触发网络控制请求
type AlertControlRequest struct {
    AlertID           string                 `json:"alert_id" binding:"required"`
    DeviceID          string                 `json:"device_id" binding:"required"`
    AlertLevel        string                 `json:"alert_level" binding:"required,oneof=critical warning info"`
    AlertType         string                 `json:"alert_type" binding:"required"`
    AlertDescription  string                 `json:"alert_description"`
    ControlAction     string                 `json:"control_action" binding:"required"`
    ControlParams     map[string]interface{} `json:"control_params"`
    Priority          int                    `json:"priority" binding:"min=1,max=10"`
}
```

### 3.2.2 网络控制响应结构

```go
// 网络控制操作响应
type ControlResponse struct {
    ActionID          string                 `json:"action_id"`
    Status            string                 `json:"status" binding:"required,oneof=accepted rejected executing completed failed"`
    Result            map[string]interface{} `json:"result"`
    ErrorMessage      string                 `json:"error_message"`
    ExecutionTime     time.Time             `json:"execution_time"`
    ResponseTime      int                   `json:"response_time"` // 毫秒
}
```

# 4 联动机制设计

## 4.1 告警级别与修复策略映射

### 4.1.1 告警级别定义

* **Critical（严重）**：需要立即修复的告警，自动触发修复操作
* **Warning（警告）**：需要关注但可延迟处理的告警，根据策略决定是否自动修复
* **Info（信息）**：仅用于信息记录的告警，不触发自动修复

### 4.1.2 修复策略配置

```yaml
# 告警级别修复策略配置
alert_remediation_policies:
  critical:
    auto_remediate: true
    response_time: 30s
    escalation_threshold: 5m
    actions:
      - type: "interface_reset"
        conditions:
          - alert_type: "interface_down"
          - alert_type: "interface_errors"
      - type: "port_shutdown"
        conditions:
          - alert_type: "port_flapping"
      - type: "config_rollback"
        conditions:
          - alert_type: "config_error"
  
  warning:
    auto_remediate: false
    manual_review: true
    escalation_threshold: 15m
    actions:
      - type: "interface_monitor"
        conditions:
          - alert_type: "interface_utilization"
  
  info:
    auto_remediate: false
    manual_review: false
    actions: []
```

## 4.2 联动触发机制

### 4.2.1 自动触发条件

* **告警级别**：Critical级别告警自动触发修复操作
* **告警类型**：特定类型的告警触发对应的修复策略
* **设备状态**：设备在线且可控制时触发修复操作
* **时间窗口**：在允许的维护时间窗口内触发操作

### 4.2.2 手动触发机制

* **管理员确认**：Warning级别告警需要管理员确认后触发
* **批量操作**：支持批量告警的联动处理
* **策略覆盖**：支持临时策略覆盖默认联动规则

## 4.3 修复操作类型

### 4.3.1 接口操作

* **接口重置**：重置故障接口，适用于接口Down或错误率过高
* **接口关闭**：关闭频繁震荡的接口，防止影响网络稳定性
* **接口监控**：增强对特定接口的监控频率

### 4.3.2 配置操作

* **配置回滚**：回滚到上一个稳定配置版本
* **配置验证**：验证当前配置的正确性
* **配置备份**：备份当前配置作为回滚点

### 4.3.3 设备操作

* **设备重启**：重启故障设备（谨慎使用）
* **设备隔离**：隔离故障设备，防止影响扩大
* **设备诊断**：执行设备诊断命令收集故障信息

# 5 联动API设计

## 5.1 告警触发控制API

### 5.1.1 告警修复触发

```go
// POST /api/v1/alerts/{alert_id}/remediate
// @Summary 触发告警修复操作
// @Description 根据告警信息触发网络控制系统的修复操作
// @Tags 告警联动
// @Accept json
// @Produce json
// @Param alert_id path string true "告警ID"
// @Param request body AlertControlRequest true "修复请求参数"
// @Success 200 {object} Response{data=ControlResponse} "修复操作已触发"
// @Failure 400 {object} Response{data=ErrorResponse} "请求参数错误"
// @Failure 404 {object} Response{data=ErrorResponse} "告警不存在"
// @Failure 500 {object} Response{data=ErrorResponse} "服务器内部错误"
// @Router /api/v1/alerts/{alert_id}/remediate [post]
func TriggerAlertRemediation(c *gin.Context) {
    // 实现告警修复触发逻辑
}
```

### 5.1.2 批量告警修复

```go
// POST /api/v1/alerts/batch-remediate
// @Summary 批量触发告警修复
// @Description 批量触发多个告警的修复操作
// @Tags 告警联动
// @Accept json
// @Produce json
// @Param request body BatchRemediationRequest true "批量修复请求"
// @Success 200 {object} Response{data=[]ControlResponse} "批量修复操作已触发"
// @Failure 400 {object} Response{data=ErrorResponse} "请求参数错误"
// @Failure 500 {object} Response{data=ErrorResponse} "服务器内部错误"
// @Router /api/v1/alerts/batch-remediate [post]
func BatchTriggerRemediation(c *gin.Context) {
    // 实现批量告警修复逻辑
}
```

## 5.2 控制状态查询API

### 5.2.1 修复状态查询

```go
// GET /api/v1/alerts/{alert_id}/remediation-status
// @Summary 查询告警修复状态
// @Description 查询指定告警的修复操作执行状态
// @Tags 告警联动
// @Accept json
// @Produce json
// @Param alert_id path string true "告警ID"
// @Success 200 {object} Response{data=RemediationStatus} "修复状态信息"
// @Failure 404 {object} Response{data=ErrorResponse} "告警不存在"
// @Router /api/v1/alerts/{alert_id}/remediation-status [get]
func GetRemediationStatus(c *gin.Context) {
    // 实现修复状态查询逻辑
}
```

### 5.2.2 控制操作历史

```go
// GET /api/v1/alerts/{alert_id}/control-history
// @Summary 查询告警控制操作历史
// @Description 查询指定告警的所有控制操作历史记录
// @Tags 告警联动
// @Accept json
// @Produce json
// @Param alert_id path string true "告警ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} Response{data=PaginatedResponse{items=[]ControlHistory}} "控制操作历史"
// @Failure 404 {object} Response{data=ErrorResponse} "告警不存在"
// @Router /api/v1/alerts/{alert_id}/control-history [get]
func GetControlHistory(c *gin.Context) {
    // 实现控制操作历史查询逻辑
}
```

## 5.3 联动配置管理API

### 5.3.1 修复策略配置

```go
// GET /api/v1/alert-remediation/policies
// @Summary 获取告警修复策略配置
// @Description 获取当前配置的告警修复策略
// @Tags 告警联动配置
// @Accept json
// @Produce json
// @Success 200 {object} Response{data=[]RemediationPolicy} "修复策略列表"
// @Router /api/v1/alert-remediation/policies [get]
func GetRemediationPolicies(c *gin.Context) {
    // 实现修复策略查询逻辑
}

// POST /api/v1/alert-remediation/policies
// @Summary 创建告警修复策略
// @Description 创建新的告警修复策略配置
// @Tags 告警联动配置
// @Accept json
// @Produce json
// @Param policy body RemediationPolicy true "修复策略配置"
// @Success 201 {object} Response{data=RemediationPolicy} "策略创建成功"
// @Failure 400 {object} Response{data=ErrorResponse} "请求参数错误"
// @Router /api/v1/alert-remediation/policies [post]
func CreateRemediationPolicy(c *gin.Context) {
    // 实现修复策略创建逻辑
}
```

# 6 联动数据处理流程

## 6.1 告警触发修复流程

```mermaid
sequenceDiagram
    participant AlertService as 告警服务
    participant PolicyEngine as 策略引擎
    participant ControlAPI as 网络控制API
    participant Device as 网络设备
    participant DB as 数据库
    
    AlertService->>PolicyEngine: 评估告警修复策略
    PolicyEngine->>PolicyEngine: 匹配修复策略
    PolicyEngine-->>AlertService: 返回修复策略
    AlertService->>ControlAPI: 发送修复请求
    ControlAPI->>ControlAPI: 验证操作权限
    ControlAPI->>Device: 执行修复操作
    Device-->>ControlAPI: 返回操作结果
    ControlAPI-->>AlertService: 返回修复结果
    AlertService->>DB: 更新告警状态
    AlertService->>DB: 记录联动日志
```

## 6.2 修复状态跟踪流程

```mermaid
sequenceDiagram
    participant AlertService as 告警服务
    participant ControlAPI as 网络控制API
    participant Scheduler as 定时调度器
    participant DB as 数据库
    
    AlertService->>ControlAPI: 查询修复状态
    ControlAPI-->>AlertService: 返回当前状态
    AlertService->>DB: 更新状态信息
    
    loop 定时检查
        Scheduler->>AlertService: 定时检查修复状态
        AlertService->>ControlAPI: 查询最新状态
        ControlAPI-->>AlertService: 返回状态更新
        AlertService->>DB: 更新状态和结果
    end
```

# 7 联动安全机制

## 7.1 权限控制

### 7.1.1 操作权限验证

* **设备权限**：验证对目标设备的操作权限
* **操作权限**：验证执行特定操作的权限级别
* **时间权限**：验证在指定时间窗口内的操作权限
* **策略权限**：验证修改联动策略的权限

### 7.1.2 操作审计

* **操作日志**：记录所有联动操作的详细日志
* **操作追踪**：支持操作的全链路追踪
* **异常告警**：异常操作触发安全告警
* **权限审计**：定期审计操作权限的合理性

## 7.2 安全防护

### 7.2.1 操作限制

* **操作频率限制**：限制短时间内对同一设备的操作频率
* **操作范围限制**：限制单次操作的影响范围
* **操作时间限制**：限制在特定时间段内的操作
* **操作确认机制**：关键操作需要二次确认

### 7.2.2 异常处理

* **操作超时处理**：设置操作超时时间，避免长时间等待
* **操作失败回滚**：操作失败时自动回滚到安全状态
* **设备状态检查**：操作前检查设备状态，避免误操作
* **网络状态监控**：操作过程中监控网络状态变化

# 8 性能优化策略

## 8.1 联动性能优化

### 8.1.1 异步处理

* **异步触发**：告警触发修复操作使用异步处理
* **状态轮询**：修复状态通过定时轮询获取
* **批量处理**：支持批量告警的联动处理
* **队列缓冲**：使用消息队列缓冲联动请求

### 8.1.2 缓存优化

* **策略缓存**：缓存修复策略配置，减少配置查询
* **状态缓存**：缓存设备状态信息，提高查询效率
* **结果缓存**：缓存修复操作结果，避免重复查询
* **权限缓存**：缓存权限验证结果，减少权限检查开销

## 8.2 数据库优化

### 8.2.1 查询优化

* **索引优化**：为联动字段建立合适的索引
* **查询分页**：大量数据查询使用分页机制
* **查询缓存**：频繁查询结果进行缓存
* **查询优化**：优化复杂查询的执行计划

### 8.2.2 存储优化

* **数据分区**：按时间分区存储联动数据
* **数据压缩**：对历史联动数据进行压缩存储
* **数据归档**：定期归档历史联动数据
* **数据清理**：定期清理无效的联动数据

# 9 监控与运维

## 9.1 联动监控指标

### 9.1.1 性能指标

* **联动响应时间**：告警触发到控制响应的时间
* **联动成功率**：联动操作的成功率统计
* **联动处理量**：单位时间内处理的联动请求数量
* **联动错误率**：联动操作的错误率统计

### 9.1.2 业务指标

* **告警修复率**：通过联动修复的告警比例
* **修复效果评估**：修复操作对告警解决的效果评估
* **联动策略命中率**：联动策略的匹配命中率
* **联动覆盖范围**：支持联动处理的告警类型覆盖范围

## 9.2 运维工具

### 9.2.1 联动状态监控

* **实时状态监控**：监控当前所有联动操作的状态
* **历史数据分析**：分析历史联动数据，优化联动策略
* **性能趋势分析**：分析联动性能的趋势变化
* **异常告警**：联动异常时及时告警通知

### 9.2.2 联动管理工具

* **联动策略管理**：提供联动策略的配置管理界面
* **联动日志查询**：提供联动操作日志的查询工具
* **联动测试工具**：提供联动功能的测试工具
* **联动故障排查**：提供联动故障的排查工具

# 10 总结

本技术方案设计为DCI系统提供了完整的网络控制与prometheus指标类告警联动机制。通过设计联动字段、API接口、处理流程和安全机制，实现了告警系统与网络控制系统的协同工作。该方案具有良好的可扩展性和可维护性，能够支撑未来新增的联动场景需求。
