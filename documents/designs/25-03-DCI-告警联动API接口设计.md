---
title: |
  DCI告警联动API接口设计

subtitle: |
  告警系统联动功能API接口详细设计
---

# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-01-27 | 顾铠羟 | 初始版本           |

# 1 概述

## 1.1 设计背景

告警联动功能需要提供完整的API接口支持，包括联动数据查询、状态更新、策略配置管理等功能。本设计文档详细定义了所有联动相关的API接口规范。

## 1.2 设计目标

* 提供完整的告警联动API接口设计
* 支持联动数据的查询和管理
* 支持联动策略的配置和管理
* 支持联动状态的跟踪和更新
* 确保API接口的规范性、一致性和易用性

## 1.3 设计原则

* **RESTful设计**：遵循RESTful API设计规范
* **统一响应格式**：所有API使用统一的响应格式
* **完整文档化**：所有API提供完整的Swagger文档
* **错误处理**：提供完善的错误处理和状态码

# 2 API基础规范

## 2.1 请求格式规范

### 2.1.1 请求头规范

```http
Content-Type: application/json
Accept: application/json
Authorization: Bearer <token>
X-Request-ID: <request_id>
```

### 2.1.2 请求参数规范

* **路径参数**：使用小写字母和连字符
* **查询参数**：使用snake_case命名
* **请求体**：使用JSON格式，字段使用camelCase命名

## 2.2 响应格式规范

### 2.2.1 成功响应格式

```json
{
  "code": 200,
  "data": {
    // 响应数据
  }
}
```

### 2.2.2 错误响应格式

```json
{
  "code": 400,
  "data": {
    "error": "错误详细描述",
    "request_id": "123e4567-e89b-12d3-a456-426614174000"
  }
}
```

## 2.3 分页响应格式

```json
{
  "code": 200,
  "data": {
    "items": [...],
    "total": 100,
    "page": 1,
    "pageSize": 20,
    "pageCount": 5
  }
}
```

# 3 联动数据查询API

## 3.1 任务关联告警查询

### 3.1.1 查询任务关联告警

```go
// GET /api/v1/alerts/task/{task_session_id}
// @Summary 查询任务关联告警
// @Description 查询指定任务会话关联的所有告警信息
// @Tags 告警联动
// @Accept json
// @Produce json
// @Param task_session_id path string true "任务会话ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param status query string false "告警状态" Enums(active, acknowledged, resolved)
// @Param level query string false "告警级别" Enums(critical, warning, info)
// @Success 200 {object} Response{data=TaskAlertsResponse} "任务关联告警列表"
// @Failure 400 {object} Response{data=ErrorResponse} "请求参数错误"
// @Failure 404 {object} Response{data=ErrorResponse} "任务不存在"
// @Router /api/v1/alerts/task/{task_session_id} [get]
func GetTaskAlerts(c *gin.Context) {
    // 实现任务关联告警查询逻辑
}

// 响应数据结构
type TaskAlertsResponse struct {
    TaskInfo    TaskInfo     `json:"task_info"`
    Alerts      []AlertInfo  `json:"alerts"`
    Statistics  AlertStats   `json:"statistics"`
    Pagination  Pagination   `json:"pagination"`
}

type TaskInfo struct {
    SessionID   string    `json:"session_id"`
    Name        string    `json:"name"`
    Status      string    `json:"status"`
    StartTime   time.Time `json:"start_time"`
    EndTime     time.Time `json:"end_time"`
    Description string    `json:"description"`
}

type AlertStats struct {
    TotalAlerts     int `json:"total_alerts"`
    CriticalAlerts  int `json:"critical_alerts"`
    WarningAlerts   int `json:"warning_alerts"`
    InfoAlerts      int `json:"info_alerts"`
    ResolvedAlerts  int `json:"resolved_alerts"`
}
```

### 3.1.2 查询任务告警统计

```go
// GET /api/v1/alerts/task/{task_session_id}/statistics
// @Summary 查询任务告警统计
// @Description 查询指定任务会话的告警统计信息
// @Tags 告警联动
// @Accept json
// @Produce json
// @Param task_session_id path string true "任务会话ID"
// @Success 200 {object} Response{data=TaskAlertStatistics} "任务告警统计"
// @Failure 404 {object} Response{data=ErrorResponse} "任务不存在"
// @Router /api/v1/alerts/task/{task_session_id}/statistics [get]
func GetTaskAlertStatistics(c *gin.Context) {
    // 实现任务告警统计查询逻辑
}

type TaskAlertStatistics struct {
    TaskInfo        TaskInfo           `json:"task_info"`
    AlertCounts     map[string]int     `json:"alert_counts"`
    AlertTrends     []AlertTrend       `json:"alert_trends"`
    CorrelationInfo CorrelationInfo    `json:"correlation_info"`
}

type AlertTrend struct {
    TimeSlot    string `json:"time_slot"`
    Critical    int    `json:"critical"`
    Warning     int    `json:"warning"`
    Info        int    `json:"info"`
}

type CorrelationInfo struct {
    CorrelationScore float64 `json:"correlation_score"`
    ImpactScope      string  `json:"impact_scope"`
    RootCause        string  `json:"root_cause"`
}
```

## 3.2 配置变更关联告警查询

### 3.2.1 查询配置变更关联告警

```go
// GET /api/v1/alerts/config-change/{config_change_id}
// @Summary 查询配置变更关联告警
// @Description 查询指定配置变更关联的所有告警信息
// @Tags 告警联动
// @Accept json
// @Produce json
// @Param config_change_id path string true "配置变更ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param min_correlation query float64 false "最小相关性评分" minimum(0) maximum(1)
// @Success 200 {object} Response{data=ConfigChangeAlertsResponse} "配置变更关联告警列表"
// @Failure 400 {object} Response{data=ErrorResponse} "请求参数错误"
// @Failure 404 {object} Response{data=ErrorResponse} "配置变更不存在"
// @Router /api/v1/alerts/config-change/{config_change_id} [get]
func GetConfigChangeAlerts(c *gin.Context) {
    // 实现配置变更关联告警查询逻辑
}

type ConfigChangeAlertsResponse struct {
    ChangeInfo  ConfigChangeInfo `json:"change_info"`
    Alerts      []AlertInfo      `json:"alerts"`
    Correlation float64           `json:"correlation_score"`
    Pagination  Pagination       `json:"pagination"`
}

type ConfigChangeInfo struct {
    ChangeID      string    `json:"change_id"`
    ChangeType    string    `json:"change_type"`
    DeviceID      string    `json:"device_id"`
    ChangeTime    time.Time `json:"change_time"`
    ChangeContent string    `json:"change_content"`
    Status        string    `json:"status"`
}
```

### 3.2.2 查询配置变更影响分析

```go
// GET /api/v1/alerts/config-change/{config_change_id}/impact-analysis
// @Summary 查询配置变更影响分析
// @Description 分析指定配置变更对告警的影响
// @Tags 告警联动
// @Accept json
// @Produce json
// @Param config_change_id path string true "配置变更ID"
// @Success 200 {object} Response{data=ImpactAnalysis} "影响分析结果"
// @Failure 404 {object} Response{data=ErrorResponse} "配置变更不存在"
// @Router /api/v1/alerts/config-change/{config_change_id}/impact-analysis [get]
func GetConfigChangeImpactAnalysis(c *gin.Context) {
    // 实现配置变更影响分析查询逻辑
}

type ImpactAnalysis struct {
    ChangeInfo       ConfigChangeInfo `json:"change_info"`
    ImpactScore      float64          `json:"impact_score"`
    AffectedAlerts   int              `json:"affected_alerts"`
    ImpactTimeline   []ImpactEvent    `json:"impact_timeline"`
    Recommendations  []string          `json:"recommendations"`
}

type ImpactEvent struct {
    Time        time.Time `json:"time"`
    EventType   string    `json:"event_type"`
    Description string    `json:"description"`
    Severity    string    `json:"severity"`
}
```

## 3.3 网络控制联动查询

### 3.3.1 查询告警修复状态

```go
// GET /api/v1/alerts/{alert_id}/remediation-status
// @Summary 查询告警修复状态
// @Description 查询指定告警的修复操作执行状态
// @Tags 告警联动
// @Accept json
// @Produce json
// @Param alert_id path string true "告警ID"
// @Success 200 {object} Response{data=RemediationStatus} "修复状态信息"
// @Failure 404 {object} Response{data=ErrorResponse} "告警不存在"
// @Router /api/v1/alerts/{alert_id}/remediation-status [get]
func GetRemediationStatus(c *gin.Context) {
    // 实现修复状态查询逻辑
}

type RemediationStatus struct {
    AlertID           string                 `json:"alert_id"`
    RemediationTaskID string                 `json:"remediation_task_id"`
    Status            string                 `json:"status"`
    CurrentStep       string                 `json:"current_step"`
    Progress          int                    `json:"progress"`
    StartTime         time.Time              `json:"start_time"`
    EndTime           time.Time              `json:"end_time"`
    Duration          int                    `json:"duration"`
    ResultSummary     string                 `json:"result_summary"`
    ErrorDetails      string                 `json:"error_details"`
    Actions           []RemediationAction    `json:"actions"`
}

type RemediationAction struct {
    ActionID      string                 `json:"action_id"`
    ActionType    string                 `json:"action_type"`
    Status        string                 `json:"status"`
    Parameters    map[string]interface{} `json:"parameters"`
    Result        map[string]interface{} `json:"result"`
    ExecutedAt    time.Time              `json:"executed_at"`
    Duration      int                    `json:"duration"`
}
```

### 3.3.2 查询控制操作历史

```go
// GET /api/v1/alerts/{alert_id}/control-history
// @Summary 查询告警控制操作历史
// @Description 查询指定告警的所有控制操作历史记录
// @Tags 告警联动
// @Accept json
// @Produce json
// @Param alert_id path string true "告警ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param action_type query string false "操作类型"
// @Param status query string false "操作状态" Enums(pending, executing, completed, failed)
// @Success 200 {object} Response{data=PaginatedResponse{items=[]ControlHistory}} "控制操作历史"
// @Failure 404 {object} Response{data=ErrorResponse} "告警不存在"
// @Router /api/v1/alerts/{alert_id}/control-history [get]
func GetControlHistory(c *gin.Context) {
    // 实现控制操作历史查询逻辑
}

type ControlHistory struct {
    ID            string                 `json:"id"`
    AlertID       string                 `json:"alert_id"`
    ActionID      string                 `json:"action_id"`
    ActionType    string                 `json:"action_type"`
    Status        string                 `json:"status"`
    Parameters    map[string]interface{} `json:"parameters"`
    Result        map[string]interface{} `json:"result"`
    ErrorMessage  string                 `json:"error_message"`
    ResponseTime  int                    `json:"response_time"`
    ExecutedAt    time.Time              `json:"executed_at"`
    CreatedAt     time.Time              `json:"created_at"`
}
```

# 4 联动状态更新API

## 4.1 任务状态同步

### 4.1.1 同步任务状态

```go
// POST /api/v1/alerts/task/{task_session_id}/sync
// @Summary 同步任务状态
// @Description 同步网络自动化任务的状态信息到告警系统
// @Tags 告警联动
// @Accept json
// @Produce json
// @Param task_session_id path string true "任务会话ID"
// @Param request body TaskSyncRequest true "任务状态同步请求"
// @Success 200 {object} Response{data=TaskSyncResponse} "任务状态同步成功"
// @Failure 400 {object} Response{data=ErrorResponse} "请求参数错误"
// @Failure 404 {object} Response{data=ErrorResponse} "任务不存在"
// @Router /api/v1/alerts/task/{task_session_id}/sync [post]
func SyncTaskStatus(c *gin.Context) {
    // 实现任务状态同步逻辑
}

type TaskSyncRequest struct {
    Status      string    `json:"status" binding:"required,oneof=running completed failed cancelled"`
    EndTime     time.Time `json:"end_time"`
    Summary     string    `json:"summary"`
    ErrorInfo   string    `json:"error_info"`
    ImpactScope string    `json:"impact_scope"`
}

type TaskSyncResponse struct {
    TaskSessionID string    `json:"task_session_id"`
    SyncTime      time.Time `json:"sync_time"`
    UpdatedAlerts int       `json:"updated_alerts"`
    Status        string    `json:"status"`
}
```

### 4.1.2 批量同步任务状态

```go
// POST /api/v1/alerts/task/batch-sync
// @Summary 批量同步任务状态
// @Description 批量同步多个任务的状态信息
// @Tags 告警联动
// @Accept json
// @Produce json
// @Param request body BatchTaskSyncRequest true "批量任务状态同步请求"
// @Success 200 {object} Response{data=BatchTaskSyncResponse} "批量任务状态同步成功"
// @Failure 400 {object} Response{data=ErrorResponse} "请求参数错误"
// @Router /api/v1/alerts/task/batch-sync [post]
func BatchSyncTaskStatus(c *gin.Context) {
    // 实现批量任务状态同步逻辑
}

type BatchTaskSyncRequest struct {
    Tasks []TaskSyncRequest `json:"tasks" binding:"required,min=1,max=100"`
}

type BatchTaskSyncResponse struct {
    TotalTasks    int                    `json:"total_tasks"`
    SuccessTasks  int                    `json:"success_tasks"`
    FailedTasks   int                    `json:"failed_tasks"`
    Results       []TaskSyncResponse     `json:"results"`
    Errors        []TaskSyncError        `json:"errors"`
}

type TaskSyncError struct {
    TaskSessionID string `json:"task_session_id"`
    Error         string `json:"error"`
}
```

## 4.2 修复状态更新

### 4.2.1 更新修复状态

```go
// POST /api/v1/alerts/{alert_id}/remediation
// @Summary 更新告警修复状态
// @Description 更新指定告警的修复操作状态
// @Tags 告警联动
// @Accept json
// @Produce json
// @Param alert_id path string true "告警ID"
// @Param request body RemediationUpdateRequest true "修复状态更新请求"
// @Success 200 {object} Response{data=RemediationUpdateResponse} "修复状态更新成功"
// @Failure 400 {object} Response{data=ErrorResponse} "请求参数错误"
// @Failure 404 {object} Response{data=ErrorResponse} "告警不存在"
// @Router /api/v1/alerts/{alert_id}/remediation [post]
func UpdateRemediationStatus(c *gin.Context) {
    // 实现修复状态更新逻辑
}

type RemediationUpdateRequest struct {
    ActionID        string                 `json:"action_id" binding:"required"`
    Status          string                 `json:"status" binding:"required,oneof=started executing completed failed cancelled"`
    CurrentStep     string                 `json:"current_step"`
    Progress        int                    `json:"progress" binding:"min=0,max=100"`
    ResultSummary   string                 `json:"result_summary"`
    ErrorDetails    string                 `json:"error_details"`
    Actions         []RemediationAction    `json:"actions"`
}

type RemediationUpdateResponse struct {
    AlertID         string    `json:"alert_id"`
    RemediationID   string    `json:"remediation_id"`
    Status          string    `json:"status"`
    UpdateTime      time.Time `json:"update_time"`
    NextStep        string    `json:"next_step"`
}
```

### 4.2.2 批量更新修复状态

```go
// POST /api/v1/alerts/batch-remediation
// @Summary 批量更新修复状态
// @Description 批量更新多个告警的修复状态
// @Tags 告警联动
// @Accept json
// @Produce json
// @Param request body BatchRemediationUpdateRequest true "批量修复状态更新请求"
// @Success 200 {object} Response{data=BatchRemediationUpdateResponse} "批量修复状态更新成功"
// @Failure 400 {object} Response{data=ErrorResponse} "请求参数错误"
// @Router /api/v1/alerts/batch-remediation [post]
func BatchUpdateRemediationStatus(c *gin.Context) {
    // 实现批量修复状态更新逻辑
}

type BatchRemediationUpdateRequest struct {
    Updates []RemediationUpdateRequest `json:"updates" binding:"required,min=1,max=50"`
}

type BatchRemediationUpdateResponse struct {
    TotalUpdates   int                           `json:"total_updates"`
    SuccessUpdates int                           `json:"success_updates"`
    FailedUpdates  int                           `json:"failed_updates"`
    Results        []RemediationUpdateResponse   `json:"results"`
    Errors         []RemediationUpdateError      `json:"errors"`
}

type RemediationUpdateError struct {
    AlertID string `json:"alert_id"`
    Error   string `json:"error"`
}
```

# 5 联动配置管理API

## 5.1 修复策略配置管理

### 5.1.1 获取修复策略列表

```go
// GET /api/v1/alert-remediation/policies
// @Summary 获取告警修复策略配置
// @Description 获取当前配置的告警修复策略列表
// @Tags 告警联动配置
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param alert_level query string false "告警级别" Enums(critical, warning, info)
// @Param alert_type query string false "告警类型"
// @Param enabled query bool false "是否启用"
// @Success 200 {object} Response{data=PaginatedResponse{items=[]RemediationPolicy}} "修复策略列表"
// @Router /api/v1/alert-remediation/policies [get]
func GetRemediationPolicies(c *gin.Context) {
    // 实现修复策略查询逻辑
}

type RemediationPolicy struct {
    ID                string                 `json:"id"`
    Name              string                 `json:"name"`
    Description       string                 `json:"description"`
    AlertLevel        string                 `json:"alert_level"`
    AlertType         string                 `json:"alert_type"`
    AutoRemediate     bool                   `json:"auto_remediate"`
    ResponseTime      int                    `json:"response_time"`
    EscalationThreshold int                 `json:"escalation_threshold"`
    Actions           []RemediationAction    `json:"actions"`
    Conditions        map[string]interface{} `json:"conditions"`
    Priority          int                    `json:"priority"`
    Enabled           bool                   `json:"enabled"`
    CreatedAt         time.Time              `json:"created_at"`
    UpdatedAt         time.Time              `json:"updated_at"`
}
```

### 5.1.2 创建修复策略

```go
// POST /api/v1/alert-remediation/policies
// @Summary 创建告警修复策略
// @Description 创建新的告警修复策略配置
// @Tags 告警联动配置
// @Accept json
// @Produce json
// @Param policy body CreateRemediationPolicyRequest true "修复策略配置"
// @Success 201 {object} Response{data=RemediationPolicy} "策略创建成功"
// @Failure 400 {object} Response{data=ErrorResponse} "请求参数错误"
// @Failure 409 {object} Response{data=ErrorResponse} "策略已存在"
// @Router /api/v1/alert-remediation/policies [post]
func CreateRemediationPolicy(c *gin.Context) {
    // 实现修复策略创建逻辑
}

type CreateRemediationPolicyRequest struct {
    Name              string                 `json:"name" binding:"required,min=1,max=255"`
    Description       string                 `json:"description"`
    AlertLevel        string                 `json:"alert_level" binding:"required,oneof=critical warning info"`
    AlertType         string                 `json:"alert_type" binding:"required"`
    AutoRemediate     bool                   `json:"auto_remediate"`
    ResponseTime      int                    `json:"response_time" binding:"min=1"`
    EscalationThreshold int                 `json:"escalation_threshold" binding:"min=1"`
    Actions           []RemediationAction    `json:"actions" binding:"required,min=1"`
    Conditions        map[string]interface{} `json:"conditions"`
    Priority          int                    `json:"priority" binding:"min=1,max=10"`
}
```

### 5.1.3 更新修复策略

```go
// PUT /api/v1/alert-remediation/policies/{policy_id}
// @Summary 更新告警修复策略
// @Description 更新指定的告警修复策略配置
// @Tags 告警联动配置
// @Accept json
// @Produce json
// @Param policy_id path string true "策略ID"
// @Param policy body UpdateRemediationPolicyRequest true "修复策略更新配置"
// @Success 200 {object} Response{data=RemediationPolicy} "策略更新成功"
// @Failure 400 {object} Response{data=ErrorResponse} "请求参数错误"
// @Failure 404 {object} Response{data=ErrorResponse} "策略不存在"
// @Router /api/v1/alert-remediation/policies/{policy_id} [put]
func UpdateRemediationPolicy(c *gin.Context) {
    // 实现修复策略更新逻辑
}

type UpdateRemediationPolicyRequest struct {
    Name              string                 `json:"name" binding:"required,min=1,max=255"`
    Description       string                 `json:"description"`
    AlertLevel        string                 `json:"alert_level" binding:"required,oneof=critical warning info"`
    AlertType         string                 `json:"alert_type" binding:"required"`
    AutoRemediate     bool                   `json:"auto_remediate"`
    ResponseTime      int                    `json:"response_time" binding:"min=1"`
    EscalationThreshold int                 `json:"escalation_threshold" binding:"min=1"`
    Actions           []RemediationAction    `json:"actions" binding:"required,min=1"`
    Conditions        map[string]interface{} `json:"conditions"`
    Priority          int                    `json:"priority" binding:"min=1,max=10"`
    Enabled           bool                   `json:"enabled"`
}
```

### 5.1.4 删除修复策略

```go
// DELETE /api/v1/alert-remediation/policies/{policy_id}
// @Summary 删除告警修复策略
// @Description 删除指定的告警修复策略配置
// @Tags 告警联动配置
// @Accept json
// @Produce json
// @Param policy_id path string true "策略ID"
// @Success 204 "策略删除成功"
// @Failure 404 {object} Response{data=ErrorResponse} "策略不存在"
// @Router /api/v1/alert-remediation/policies/{policy_id} [delete]
func DeleteRemediationPolicy(c *gin.Context) {
    // 实现修复策略删除逻辑
}
```

## 5.2 升级策略配置管理

### 5.2.1 获取升级策略列表

```go
// GET /api/v1/alert-escalation/policies
// @Summary 获取告警升级策略配置
// @Description 获取当前配置的告警升级策略列表
// @Tags 告警联动配置
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param alert_level query string false "告警级别" Enums(critical, warning, info)
// @Param enabled query bool false "是否启用"
// @Success 200 {object} Response{data=PaginatedResponse{items=[]EscalationPolicy}} "升级策略列表"
// @Router /api/v1/alert-escalation/policies [get]
func GetEscalationPolicies(c *gin.Context) {
    // 实现升级策略查询逻辑
}

type EscalationPolicy struct {
    ID                  string                 `json:"id"`
    Name                string                 `json:"name"`
    Description         string                 `json:"description"`
    AlertLevel          string                 `json:"alert_level"`
    EscalationLevel     int                    `json:"escalation_level"`
    EscalationTime      int                    `json:"escalation_time"`
    EscalationActions   []EscalationAction    `json:"escalation_actions"`
    NotificationChannels map[string]interface{} `json:"notification_channels"`
    Enabled             bool                   `json:"enabled"`
    CreatedAt           time.Time              `json:"created_at"`
    UpdatedAt           time.Time              `json:"updated_at"`
}

type EscalationAction struct {
    ActionType    string                 `json:"action_type"`
    Parameters    map[string]interface{} `json:"parameters"`
    Conditions    map[string]interface{} `json:"conditions"`
}
```

### 5.2.2 创建升级策略

```go
// POST /api/v1/alert-escalation/policies
// @Summary 创建告警升级策略
// @Description 创建新的告警升级策略配置
// @Tags 告警联动配置
// @Accept json
// @Produce json
// @Param policy body CreateEscalationPolicyRequest true "升级策略配置"
// @Success 201 {object} Response{data=EscalationPolicy} "策略创建成功"
// @Failure 400 {object} Response{data=ErrorResponse} "请求参数错误"
// @Router /api/v1/alert-escalation/policies [post]
func CreateEscalationPolicy(c *gin.Context) {
    // 实现升级策略创建逻辑
}

type CreateEscalationPolicyRequest struct {
    Name                string                 `json:"name" binding:"required,min=1,max=255"`
    Description         string                 `json:"description"`
    AlertLevel          string                 `json:"alert_level" binding:"required,oneof=critical warning info"`
    EscalationLevel     int                    `json:"escalation_level" binding:"min=1"`
    EscalationTime      int                    `json:"escalation_time" binding:"min=1"`
    EscalationActions   []EscalationAction    `json:"escalation_actions" binding:"required,min=1"`
    NotificationChannels map[string]interface{} `json:"notification_channels"`
}
```

# 6 联动触发API

## 6.1 告警修复触发

### 6.1.1 触发单个告警修复

```go
// POST /api/v1/alerts/{alert_id}/remediate
// @Summary 触发告警修复操作
// @Description 根据告警信息触发网络控制系统的修复操作
// @Tags 告警联动
// @Accept json
// @Produce json
// @Param alert_id path string true "告警ID"
// @Param request body AlertControlRequest true "修复请求参数"
// @Success 200 {object} Response{data=ControlResponse} "修复操作已触发"
// @Failure 400 {object} Response{data=ErrorResponse} "请求参数错误"
// @Failure 404 {object} Response{data=ErrorResponse} "告警不存在"
// @Failure 500 {object} Response{data=ErrorResponse} "服务器内部错误"
// @Router /api/v1/alerts/{alert_id}/remediate [post]
func TriggerAlertRemediation(c *gin.Context) {
    // 实现告警修复触发逻辑
}

type AlertControlRequest struct {
    DeviceID          string                 `json:"device_id" binding:"required"`
    AlertLevel        string                 `json:"alert_level" binding:"required,oneof=critical warning info"`
    AlertType         string                 `json:"alert_type" binding:"required"`
    AlertDescription  string                 `json:"alert_description"`
    ControlAction     string                 `json:"control_action" binding:"required"`
    ControlParams     map[string]interface{} `json:"control_params"`
    Priority          int                    `json:"priority" binding:"min=1,max=10"`
}

type ControlResponse struct {
    ActionID          string                 `json:"action_id"`
    Status            string                 `json:"status"`
    Result            map[string]interface{} `json:"result"`
    ErrorMessage      string                 `json:"error_message"`
    ExecutionTime     time.Time             `json:"execution_time"`
    ResponseTime      int                   `json:"response_time"`
}
```

### 6.1.2 批量触发告警修复

```go
// POST /api/v1/alerts/batch-remediate
// @Summary 批量触发告警修复
// @Description 批量触发多个告警的修复操作
// @Tags 告警联动
// @Accept json
// @Produce json
// @Param request body BatchRemediationRequest true "批量修复请求"
// @Success 200 {object} Response{data=[]ControlResponse} "批量修复操作已触发"
// @Failure 400 {object} Response{data=ErrorResponse} "请求参数错误"
// @Failure 500 {object} Response{data=ErrorResponse} "服务器内部错误"
// @Router /api/v1/alerts/batch-remediate [post]
func BatchTriggerRemediation(c *gin.Context) {
    // 实现批量告警修复逻辑
}

type BatchRemediationRequest struct {
    Alerts []AlertControlRequest `json:"alerts" binding:"required,min=1,max=50"`
}
```

## 6.2 告警升级触发

### 6.2.1 触发告警升级

```go
// POST /api/v1/alerts/{alert_id}/escalate
// @Summary 触发告警升级
// @Description 根据告警级别和策略触发告警升级操作
// @Tags 告警联动
// @Accept json
// @Produce json
// @Param alert_id path string true "告警ID"
// @Param request body EscalationRequest true "升级请求参数"
// @Success 200 {object} Response{data=EscalationResponse} "升级操作已触发"
// @Failure 400 {object} Response{data=ErrorResponse} "请求参数错误"
// @Failure 404 {object} Response{data=ErrorResponse} "告警不存在"
// @Router /api/v1/alerts/{alert_id}/escalate [post]
func TriggerAlertEscalation(c *gin.Context) {
    // 实现告警升级触发逻辑
}

type EscalationRequest struct {
    EscalationLevel     int                    `json:"escalation_level" binding:"min=1"`
    EscalationReason    string                 `json:"escalation_reason"`
    NotificationChannels []string              `json:"notification_channels"`
    CustomActions       []EscalationAction    `json:"custom_actions"`
}

type EscalationResponse struct {
    EscalationID        string                 `json:"escalation_id"`
    Status              string                 `json:"status"`
    EscalationLevel     int                    `json:"escalation_level"`
    TriggeredActions    []EscalationAction    `json:"triggered_actions"`
    NotificationSent    bool                   `json:"notification_sent"`
    TriggerTime         time.Time              `json:"trigger_time"`
}
```

# 7 联动统计API

## 7.1 联动效果统计

### 7.1.1 查询联动效果统计

```go
// GET /api/v1/alert-linkage/statistics
// @Summary 查询联动效果统计
// @Description 查询告警联动效果的整体统计信息
// @Tags 告警联动统计
// @Accept json
// @Produce json
// @Param start_time query string false "开始时间" format(date-time)
// @Param end_time query string false "结束时间" format(date-time)
// @Param group_by query string false "分组方式" Enums(hour, day, week, month)
// @Success 200 {object} Response{data=LinkageStatistics} "联动效果统计"
// @Router /api/v1/alert-linkage/statistics [get]
func GetLinkageStatistics(c *gin.Context) {
    // 实现联动效果统计查询逻辑
}

type LinkageStatistics struct {
    TimeRange          TimeRange              `json:"time_range"`
    TotalAlerts        int                    `json:"total_alerts"`
    LinkedAlerts       int                    `json:"linked_alerts"`
    LinkageRate        float64                `json:"linkage_rate"`
    RemediationSuccess int                    `json:"remediation_success"`
    RemediationRate    float64                `json:"remediation_rate"`
    AverageResponseTime float64               `json:"average_response_time"`
    TopLinkageTypes    []LinkageTypeStats     `json:"top_linkage_types"`
    TrendData          []LinkageTrendData     `json:"trend_data"`
}

type TimeRange struct {
    StartTime time.Time `json:"start_time"`
    EndTime   time.Time `json:"end_time"`
}

type LinkageTypeStats struct {
    Type        string  `json:"type"`
    Count       int     `json:"count"`
    Percentage  float64 `json:"percentage"`
}

type LinkageTrendData struct {
    TimeSlot    string  `json:"time_slot"`
    AlertCount  int     `json:"alert_count"`
    LinkageCount int    `json:"linkage_count"`
    SuccessRate float64 `json:"success_rate"`
}
```

### 7.1.2 查询联动性能统计

```go
// GET /api/v1/alert-linkage/performance
// @Summary 查询联动性能统计
// @Description 查询告警联动操作的性能统计信息
// @Tags 告警联动统计
// @Accept json
// @Produce json
// @Param start_time query string false "开始时间" format(date-time)
// @Param end_time query string false "结束时间" format(date-time)
// @Success 200 {object} Response{data=LinkagePerformance} "联动性能统计"
// @Router /api/v1/alert-linkage/performance [get]
func GetLinkagePerformance(c *gin.Context) {
    // 实现联动性能统计查询逻辑
}

type LinkagePerformance struct {
    TimeRange              TimeRange              `json:"time_range"`
    AverageResponseTime    float64                `json:"average_response_time"`
    P95ResponseTime       float64                `json:"p95_response_time"`
    P99ResponseTime       float64                `json:"p99_response_time"`
    SuccessRate           float64                `json:"success_rate"`
    ErrorRate             float64                `json:"error_rate"`
    Throughput            float64                `json:"throughput"`
    PerformanceByType     []PerformanceByType    `json:"performance_by_type"`
    PerformanceByLevel    []PerformanceByLevel   `json:"performance_by_level"`
}

type PerformanceByType struct {
    LinkageType       string  `json:"linkage_type"`
    Count             int     `json:"count"`
    AverageTime       float64 `json:"average_time"`
    SuccessRate       float64 `json:"success_rate"`
}

type PerformanceByLevel struct {
    AlertLevel        string  `json:"alert_level"`
    Count             int     `json:"count"`
    AverageTime       float64 `json:"average_time"`
    SuccessRate       float64 `json:"success_rate"`
}
```

# 8 错误处理规范

## 8.1 错误码定义

```go
// 联动相关错误码
const (
    // 联动配置错误 (4000-4099)
    ErrLinkageConfigInvalid     = 4001
    ErrLinkagePolicyNotFound    = 4002
    ErrLinkagePolicyConflict    = 4003
    ErrLinkagePolicyDisabled    = 4004
    
    // 联动操作错误 (4100-4199)
    ErrLinkageOperationFailed   = 4101
    ErrLinkageTimeout           = 4102
    ErrLinkagePermissionDenied  = 4103
    ErrLinkageDeviceUnavailable = 4104
    
    // 联动状态错误 (4200-4299)
    ErrLinkageStatusInvalid     = 4201
    ErrLinkageStatusConflict    = 4202
    ErrLinkageStatusTimeout     = 4203
    
    // 联动数据错误 (4300-4399)
    ErrLinkageDataInvalid       = 4301
    ErrLinkageDataNotFound      = 4302
    ErrLinkageDataConflict      = 4303
)
```

## 8.2 错误响应示例

```json
{
  "code": 4101,
  "data": {
    "error": "联动操作执行失败：设备连接超时",
    "request_id": "123e4567-e89b-12d3-a456-426614174000",
    "details": {
      "operation_type": "interface_reset",
      "device_id": "device_001",
      "timeout": 30
    }
  }
}
```

# 9 总结

本API接口设计为DCI告警系统提供了完整的联动功能支持。通过设计联动数据查询、状态更新、配置管理和触发操作等API接口，实现了告警系统与其他业务系统的协同工作。所有API接口都遵循RESTful设计规范，提供完整的Swagger文档，确保接口的规范性、一致性和易用性。 