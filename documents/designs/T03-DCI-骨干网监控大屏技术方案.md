---
title: |
  DCI 骨干网监控大屏技术方案
subtitle: |
  DCI-Monitor 数据支持分析
---

## 1. 引言

### 1.1 文档目的

本文档旨在分析 DCI 骨干干监控大屏所需监控数据的技术实现方案，明确 DCI 数据监测系统 (DCI-Monitor) 在其中承担的数据采集、处理和供给角色。本文案作为 DCI-Monitor 总体设计的补充，用于指导相关功能的开发与集成。

### 1.2 文档范围

本文档聚焦于大屏中特定监控项目的数据来源、采集方式、存储策略以及针对拓扑展示的图数据库适用性分析。涉及的监控项目包括：端口流量、设备性能指标（CPU、内存、温度、路由条目数）、日志展示以及网络拓扑图展示。

大屏本身的 UI 布局、用户交互、自定义模块、阈值设置、刷新机制等前端实现细节不在此文档详细讨论范围内，但会提及后端需要为此提供的支持。

## 2. 监控数据项分析

### 2.1 流量监控类

#### 2.1.1 数据需求

需要展示指定端口的上行和下行实时流量速率，通常以曲线图展示。还需要在图表上或通过提示信息（Tooltip）显示流量峰值及其发生的时间点。

#### 2.1.2 数据来源与采集

端口流量数据主要通过 SNMP 从网络设备采集。DCI-Monitor 使用 Telegraf Agent (inputs.snmp) 定期轮询设备 IF-MIB 中的接口计数器 OID，优先使用 64 位计数器（如 ifHCInOctets, ifHCOutOctets）。采集频率建议设置为 10 秒或 15 秒，以满足大屏近实时展示的需求。流量速率由后端服务根据两次采样值之差和采样间隔计算得出 (bps)，再转换为 Mbps。峰值检测逻辑可在后端数据处理或查询时实现，记录指定时间窗口内的最大值及其时间戳。

#### 2.1.3 数据存储

计算出的端口流量速率（区分上下行）作为时间序列数据，存储在 TDengine 中。使用 NMS 提供的设备 ID 和端口 ID 作为核心 Tag。

#### 2.1.4 数据展示

通过 Grafana 或自研前端 UI 的曲线图 (Time Series / Line Chart) 展示。后端 API 需支持按设备端口、时间范围查询流量数据，并能提供指定时间范围内的峰值信息。

### 2.2 性能监控类

#### 2.2.1 数据需求

需要展示设备的 CPU 使用率、内存使用率、温度和路由条目总数。展示形式可以是数字图或饼状图。

#### 2.2.2 数据来源与采集

这些性能指标同样主要通过 SNMP 采集。

*   CPU 使用率: 查询 HOST-RESOURCES-MIB (.*******.********.3.1.2 hrProcessorLoad) 或设备厂商私有 MIB。通常采集的是一段时间内的平均值。
*   内存使用率: 查询 HOST-RESOURCES-MIB (.*******.********.3.1.5 hrStorageSize, .*******.********.3.1.6 hrStorageUsed) 计算，或查询私有 MIB 获取更直接的内存使用率。
*   温度: 查询 ENTITY-SENSOR-MIB (.*******.********.1.1.4 entPhySensorValue) 或厂商私有 MIB。需要根据 entPhySensorType 和 entPhySensorScale 确定单位和精度。
*   路由条目数: 查询 IP-FORWARD-MIB (.*******.********.2 ipFwdTableNumEntries) 获取 IP 路由表总数，或查询特定路由协议 MIB (如 OSPF-MIB) 获取相关路由信息。

采集频率可根据指标变化特性设置，例如 CPU/内存 1 分钟，温度 5 分钟，路由条目数 5-15 分钟。

#### 2.2.3 数据存储

这些性能指标作为时间序列数据，存储在 TDengine 中。使用 NMS 设备 ID 作为核心 Tag。

#### 2.2.4 数据展示

通过 Grafana 或自研前端 UI 的数字图 (Stat / Single Stat) 或饼图 (Pie Chart) 展示。后端 API 需支持按设备、指标类型、时间范围查询。

### 2.3 日志类

#### 2.3.1 数据需求

需要在大屏的特定区域或面板中展示与特定租户或设备相关的日志信息。

#### 2.3.2 数据来源与采集

设备日志通过 Syslog 协议发送给 DCI-Monitor 的 Syslog 接收器 (如 Telegraf outputs.kafka 或 Logstash/Fluentd input)，然后进入 Kafka 的 logs_events Topic。日志处理管道 (Logstash/Fluentd) 负责解析日志，提取关键字段（如时间戳、设备 IP、日志级别、消息内容），并根据设备 IP 查询 NMS 元数据，关联上 NMS 设备 ID 和可能的租户信息。租户日志的定义需要进一步明确，可能是指归属于某租户的设备产生的日志。

#### 2.3.3 数据存储

处理并丰富化后的日志数据存储在 Elasticsearch 中。

#### 2.3.4 数据展示

通过 Grafana (Logs Panel) 或 Kibana 或自研前端 UI 的日志展示组件呈现。后端 API 需支持按 NMS 设备 ID、租户 ID（如果可关联）、时间范围、关键词等条件查询 Elasticsearch 中的日志数据。

## 3. 拓扑展示方案分析

### 3.1 数据需求

在大屏上展示网络的拓扑结构图。关键需求点在于：

*   拓扑视图需要反映网络设备间的连接关系。
*   用户可以在大屏上拖拽调整节点的位置。
*   用户调整后的布局风格可以被保存（支持最多 5 种风格预设）。
*   (更新) 拓扑数据应能支持基本的邻接查询和可视化。

### 3.2 拓扑数据来源分析

此方案确认 DCI-Monitor 将主动发现并构建基础的网络拓扑视图。

*   主要来源: 通过 SNMP 查询网络设备的 LLDP (Link Layer Discovery Protocol) 相关 MIBs 来发现物理层或数据链路层的邻接关系。这是构建 DCI-Monitor 自身维护拓扑的基础。
    *   采集方法: DCI-Monitor Agent (Telegraf with inputs.snmp) 将定期轮询其监控范围内的设备，查询标准 `LLDP-MIB` (.*******.2.1.0.8802.1.1.2) 或特定厂商的邻居发现协议 MIB (如 `CISCO-CDP-MIB`)。
    *   关键 OIDs (LLDP 示例): `lldpLocPortId` (本地端口标识), `lldpRemChassisId` (邻居机箱 ID), `lldpRemPortId` (邻居端口标识), `lldpRemSysName` (邻居系统名称) 等。
    *   构建拓扑: 后端服务汇总所有 Agent 采集到的 LLDP/CDP 数据，解析邻接关系（哪个设备的哪个端口连接到哪个邻居设备的哪个端口），构建节点（设备）和边（连接）的数据结构。
    *   局限性: LLDP/CDP 主要反映 L2 邻接关系，构建的是物理拓扑视图。它不直接提供 L3 路由信息或更高级的逻辑连接（如 Overlay 隧道）。

*   次要或补充来源: 外部系统 API (NMS, SDN 控制器等)。
    *   这些 API 仍然是获取更丰富信息（如设备型号、位置、逻辑链路名称、应用归属）或更高层逻辑拓扑视图的重要途径，可用于**丰富** DCI-Monitor 通过 LLDP 构建的基础拓扑。
    *   如果存在权威的外部拓扑源，其数据可与 LLDP 发现的数据进行比对和融合。

因此，技术方案将以 SNMP LLDP/CDP 发现为主，辅以可能的 API 调用来获取和丰富拓扑信息。

### 3.3 图数据库适用性分析

鉴于 DCI-Monitor 需要自行通过 LLDP/CDP 数据构建和维护网络拓扑模型，引入图数据库来存储和查询这些高度关联的数据变得非常必要和高效。图数据库在此场景下的优势包括：

*   自然的数据模型: 网络设备作为节点（Vertex），物理连接作为边（Edge），属性（如设备名、端口名、IP 地址）可以附加在节点或边上。
*   高效的邻接和路径查询: 能够快速查询一个设备的所有邻居、两点间的多跳路径等，这是拓扑可视化和分析的基础。
*   支持未来扩展: 便于未来进行更复杂的拓扑分析，如故障影响范围推断、冗余路径分析等。

本项目选用 NebulaGraph 作为图数据库解决方案。主要原因如下：

1.  开源且原生支持分布式和高可用架构 (Raft 一致性)，与 DCI-Monitor 整体架构设计中对后端核心组件（Kafka, TDengine, ES）的 HA 要求一致。
2.  面向大规模图设计，具备良好的性能和水平扩展能力，能够应对未来网络规模增长。
3.  采用强类型 Property Graph 模型，需要预定义 Schema，有助于保证网络拓扑数据的一致性。
4.  查询语言 nGQL 类 SQL，相对易于上手，且在图遍历方面表达能力强。
5.  社区逐渐活跃，Go 语言客户端支持良好。

采用 NebulaGraph 后，DCI-Monitor 后端服务负责将 LLDP/CDP 采集结果解析并写入图数据库，前端通过后端 API 查询图数据库获取拓扑数据进行展示。

### 3.4 节点位置与风格存储

用户拖拽节点后的位置信息以及选择的风格（最多 5 种预设）属于用户在大屏上的个性化配置，而非拓扑数据本身。

*   存储位置: 这些布局和风格信息应存储在 DCI-Monitor 后端的 MySQL 数据库中，与特定的用户或大屏实例关联。可以设计一张表 (例如 `dashboard_topology_layouts`) 来存储布局信息 (节点 ID -> x, y 坐标) 和选择的风格名称。节点 ID 应与图数据库中存储的设备节点 ID 保持一致。
*   交互流程: 前端可视化库（如基于 D3.js, Vis.js, G6 等构建）负责处理节点拖拽事件，并在用户保存时，将计算好的节点坐标和选择的风格信息通过 API 发送给 DCI-Monitor 后端。加载大屏时，前端先通过 API 获取拓扑数据（查询图数据库），再通过另一个 API 获取用户保存的布局和风格信息（查询 MySQL），然后应用到拓扑图上。

## 4. 总结与建议

DCI-Monitor 为骨干干监控大屏提供数据的核心策略更新为：

*   通过 SNMP 高效采集端口流量和设备性能指标，存储于 TDengine。
*   接收并处理 Syslog 日志，关联元数据后存入 Elasticsearch。
*   通过 SNMP 主动发现 LLDP/CDP 邻接关系，构建物理拓扑，**存储于推荐的图数据库 (NebulaGraph)**。
*   (可选) 通过 API 与外部权威拓扑数据源集成，获取逻辑拓扑或丰富节点/链路属性。
*   利用 MySQL 存储用户自定义的拓扑布局和风格配置。

建议：

*   优先实现基于 SNMP 的 LLDP/CDP 数据采集和解析逻辑。
*   引入并部署推荐的图数据库 (NebulaGraph)，设计合理的图模型（Schema），实现拓扑数据的写入和查询 API。
*   确保与 NMS/外部拓扑源的 API 对接（如果需要）稳定可靠，明确数据格式和更新机制。
*   后端需要提供稳定、高效的数据查询 API（包括图数据库查询接口），满足大屏各类监控项目的数据请求。
*   后端需要提供用于存储和读取用户自定义拓扑布局/风格的 API。 