#!/bin/bash
# Telegraf流量数据采集及FlowData处理测试脚本

# 输出颜色设置
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认模式
KAFKA_TEST_MODE=false
# 固定运行时间 (秒)
RUN_TIME=15
# 固定模拟数据生成次数
SIMULATION_COUNT=10
# 固定SNMP MAX_REPETITIONS
SNMP_MAX_REPETITIONS=10
# OUTPUT_FILE 固定行数
OUTPUT_FILE_LINES=100
# 生产环境模式
USE_PROD_CONFIG=false

# 帮助函数
print_help() {
  echo "Usage: $0 [options]"
  echo
  echo "Telegraf SNMP数据采集测试脚本."
  echo
  echo "Options:"
  echo "  -r, --real          使用实际SNMP设备进行采集."
  echo "  -s, --simulation    使用模拟数据进行采集 (与-r互斥)."
  echo "  -t, --kafka-test    使用测试Kafka主题 (默认: 使用真实Kafka主题)."
  echo "  -d, --duration N    设置运行时间为N秒 (默认: ${RUN_TIME}秒)."
  echo "  --tmp               使用临时状态监测配置文件(telegraf_flowdata_tmp_kafka-test.conf)进行测试."
  echo "  --complete          使用综合配置文件(包含流量+状态监控)进行测试."
  echo "  --prod              使用生产环境配置文件(telegraf_flowdata_prod.conf)进行测试."
  echo "  --kafka-only        仅测试Kafka连通性，基于telegraf_flowdata_complete.conf中的kafka配置."
  echo "  -h, --help          显示此帮助信息并退出."
  echo
  echo "测试场景组合:"
  echo "  $0 -s             # 模拟数据 -> 真实Kafka主题"
  echo "  $0 -s -t          # 模拟数据 -> 测试Kafka主题"
  echo "  $0 -r             # 真实数据 -> 真实Kafka主题 (telegraf_flowdata_real.conf)"
  echo "  $0 -r -t          # 真实数据 -> 测试Kafka主题 (telegraf_flowdata_real_kafka-test.conf)"
  echo "  $0 --tmp          # 真实状态数据 -> 测试Kafka主题 (telegraf_flowdata_tmp_kafka-test.conf)"
  echo "  $0 --complete     # 真实综合数据 -> 真实Kafka主题 (telegraf_flowdata_complete.conf)"
  echo "  $0 --complete -t  # 真实综合数据 -> 测试Kafka主题 (telegraf_flowdata_complete_kafka-test.conf)"
  echo "  $0 --prod -d 120  # 真实数据 -> 生产环境配置 (telegraf_flowdata_prod.conf)"
  echo "  $0 --kafka-only   # 仅测试Kafka连通性 (基于telegraf_flowdata_complete.conf)"
  echo "  $0 -s -d 30       # 模拟数据，运行30秒"
  echo "  $0 -r -d 3600     # 真实数据，运行1小时"
}

# 如果没有参数，显示帮助信息
if [[ "$#" -eq 0 ]]; then
    print_help
    exit 0
fi

# 初始化SIMULATION_MODE
SIMULATION_MODE=""
USE_TMP_CONFIG=false
USE_COMPLETE_CONFIG=false
KAFKA_ONLY_MODE=false

# 处理命令行参数
while [[ "$#" -gt 0 ]]; do
  case $1 in
    -r|--real) SIMULATION_MODE=false; shift ;;
    -s|--simulation) SIMULATION_MODE=true; shift ;;
    -t|--kafka-test) KAFKA_TEST_MODE=true; shift ;;
    -d|--duration) 
      if [[ "$2" =~ ^[0-9]+$ ]]; then
        RUN_TIME=$2; shift 2
      else
        echo -e "${RED}错误: -d/--duration 参数需要一个数字值${NC}"
        print_help
        exit 1
      fi ;;
    --tmp) USE_TMP_CONFIG=true; shift ;;
    --complete) USE_COMPLETE_CONFIG=true; shift ;;
    --prod) USE_PROD_CONFIG=true; shift ;;
    --kafka-only) KAFKA_ONLY_MODE=true; shift ;;
    -h|--help) print_help; exit 0 ;;
    *) echo "未知参数: $1"; print_help; exit 1 ;;
  esac
done

# 检查是否选择了采集模式
if [[ -z "${SIMULATION_MODE}" && "$USE_TMP_CONFIG" = false && "$USE_COMPLETE_CONFIG" = false && "$USE_PROD_CONFIG" = false && "$KAFKA_ONLY_MODE" = false ]]; then
    echo -e "${RED}错误: 请选择一种采集模式 (-s/--simulation, [--complete] -r/--real, --tmp, --prod, 或 --kafka-only).${NC}"
    print_help
    exit 1
fi

# Kafka连通性测试函数
test_kafka_connectivity() {
  echo -e "${BLUE}======================================${NC}"
  echo -e "${BLUE}====== Kafka连通性测试 =============${NC}"
  echo -e "${BLUE}======================================${NC}"
  echo

  # 基于telegraf_flowdata_complete.conf配置文件
  local base_config="${CONFIG_DIR}/telegraf_flowdata_complete.conf"
  if [ ! -f "${base_config}" ]; then
    echo -e "${RED}错误: 基础配置文件未找到: ${base_config}${NC}"
    return 1
  fi

  # 创建临时的Kafka连通性测试配置文件
  local kafka_test_config="/tmp/telegraf-kafka-connectivity-test-$(date +%s)-${RANDOM}.conf"

  echo -e "${YELLOW}正在创建Kafka连通性测试配置文件...${NC}"

  # 从基础配置文件中提取kafka配置，创建最小化测试配置
  cat > "${kafka_test_config}" << 'EOF'
# Telegraf Kafka连通性测试配置 - 最小化配置
[agent]
  interval = "10s"
  round_interval = true
  metric_batch_size = 1
  metric_buffer_limit = 10
  collection_jitter = "0s"
  flush_interval = "1s"
  flush_jitter = "0s"
  precision = ""
  debug = true
  quiet = false
  hostname = "kafka-connectivity-test"
  omit_hostname = false

# 全局标签配置
[global_tags]
  test_type = "kafka_connectivity"

# 简单的输入插件 - 生成测试数据
[[inputs.internal]]
  collect_memstats = false

EOF

  # 从原配置文件中提取kafka输出配置
  echo "# Kafka输出配置 - 从${base_config}提取" >> "${kafka_test_config}"
  sed -n '/^\[\[outputs\.kafka\]\]/,/^$/p' "${base_config}" >> "${kafka_test_config}"

  echo -e "${GREEN}Kafka测试配置文件已创建: ${kafka_test_config}${NC}"
  echo -e "${YELLOW}配置文件内容预览:${NC}"
  echo "------------------------"
  head -n 20 "${kafka_test_config}"
  echo "..."
  tail -n 10 "${kafka_test_config}"
  echo "------------------------"
  echo

  # 第一步：使用telegraf --test验证配置文件语法
  echo -e "${YELLOW}步骤1: 验证Kafka配置文件语法...${NC}"
  local config_test_output="/tmp/kafka-config-test-output.log"
  ${TELEGRAF_BIN} --config ${kafka_test_config} --test > ${config_test_output} 2>&1
  local config_test_exit_code=$?

  if [ $config_test_exit_code -ne 0 ]; then
    echo -e "${RED}❌ Kafka配置文件语法错误！${NC}"
    echo "------------------------"
    cat ${config_test_output}
    echo "------------------------"
    rm -f "${kafka_test_config}" "${config_test_output}"
    return 1
  else
    echo -e "${GREEN}✅ Kafka配置文件语法验证通过${NC}"
  fi

  # 第二步：使用telegraf实际运行短时间测试连通性
  echo -e "${YELLOW}步骤2: 实际测试Kafka连通性（运行5秒）...${NC}"
  echo "命令：timeout 5s ${TELEGRAF_BIN} --config ${kafka_test_config}"

  local kafka_test_output="/tmp/kafka-connectivity-test-output.log"
  timeout 5s ${TELEGRAF_BIN} --config ${kafka_test_config} > ${kafka_test_output} 2>&1
  local kafka_test_exit_code=$?

  echo -e "${YELLOW}Kafka连通性测试输出:${NC}"
  echo "------------------------"
  cat ${kafka_test_output}
  echo "------------------------"
  echo

  # 分析测试结果
  # timeout命令的退出码：124表示超时（这是我们期望的），0表示正常结束，其他表示错误
  if [ $kafka_test_exit_code -eq 124 ]; then
    echo -e "${GREEN}✅ Kafka连通性测试成功！（程序正常运行并被超时终止）${NC}"

    # 检查输出中是否有连接错误
    if grep -qi "connection refused\|network unreachable\|no route to host" ${kafka_test_output}; then
      echo -e "${RED}❌ 网络连接问题: 无法连接到Kafka broker${NC}"
      rm -f "${kafka_test_config}" "${kafka_test_output}" "${config_test_output}"
      return 1
    elif grep -qi "authentication failed\|sasl.*failed\|invalid credentials" ${kafka_test_output}; then
      echo -e "${RED}❌ 认证问题: SASL认证失败${NC}"
      rm -f "${kafka_test_config}" "${kafka_test_output}" "${config_test_output}"
      return 1
    elif grep -qi "certificate.*error\|tls.*error\|ssl.*error" ${kafka_test_output}; then
      echo -e "${RED}❌ 证书问题: TLS/SSL证书配置错误${NC}"
      rm -f "${kafka_test_config}" "${kafka_test_output}" "${config_test_output}"
      return 1
    elif grep -qi "timeout.*connect\|connection.*timeout" ${kafka_test_output}; then
      echo -e "${RED}❌ 超时问题: 连接超时，可能是网络或防火墙问题${NC}"
      rm -f "${kafka_test_config}" "${kafka_test_output}" "${config_test_output}"
      return 1
    else
      echo -e "${GREEN}✅ Kafka连接正常，未发现连接错误${NC}"
    fi
  elif [ $kafka_test_exit_code -eq 0 ]; then
    echo -e "${GREEN}✅ Kafka连通性测试成功！（程序正常结束）${NC}"
  else
    echo -e "${RED}❌ Kafka连通性测试失败！退出码: ${kafka_test_exit_code}${NC}"

    # 分析常见错误
    if grep -qi "connection refused\|network unreachable" ${kafka_test_output}; then
      echo -e "${RED}网络连接问题: 无法连接到Kafka broker${NC}"
    elif grep -qi "authentication\|sasl\|invalid credentials" ${kafka_test_output}; then
      echo -e "${RED}认证问题: SASL/TLS配置可能有误${NC}"
    elif grep -qi "certificate\|cert\|ssl\|tls" ${kafka_test_output}; then
      echo -e "${RED}证书问题: TLS证书配置可能有误${NC}"
    elif grep -qi "timeout" ${kafka_test_output}; then
      echo -e "${RED}超时问题: 连接超时，可能是网络或防火墙问题${NC}"
    fi

    # 清理临时文件
    rm -f "${kafka_test_config}" "${kafka_test_output}" "${config_test_output}"
    return 1
  fi

  # 清理临时文件
  rm -f "${kafka_test_config}" "${kafka_test_output}"

  echo -e "${GREEN}Kafka连通性测试完成${NC}"
  echo
  return 0
}

# 设置工作目录
DCI_ROOT="/Users/<USER>/code/dci/dci-workspace"
TELEGRAF_BIN="${DCI_ROOT}/dci_self_test/bin/telegraf"
CONFIG_DIR="${DCI_ROOT}/dci_self_test/scripts/snmp/config"
TEST_RESULTS_DIR="${DCI_ROOT}/dci_self_test/results"

# 根据参数生成文件名后缀（仅用于非Kafka连通性测试模式）
FILE_SUFFIX=""
if ! $SIMULATION_MODE; then
  FILE_SUFFIX="${FILE_SUFFIX}_real"
else
  FILE_SUFFIX="${FILE_SUFFIX}_mock"
fi

if $KAFKA_TEST_MODE; then
  FILE_SUFFIX="${FILE_SUFFIX}_kafka-test"
else
  FILE_SUFFIX="${FILE_SUFFIX}_kafka-real"
fi

if $USE_TMP_CONFIG; then
  FILE_SUFFIX="_tmp"
fi

# 设置带有后缀的日志和输出文件路径
LOG_FILE="${TEST_RESULTS_DIR}/telegraf-flowdata-test${FILE_SUFFIX}.log"
OUTPUT_FILE="${TEST_RESULTS_DIR}/telegraf-flowdata-test${FILE_SUFFIX}.out"
OUTPUT_FILE_FULL="${TEST_RESULTS_DIR}/telegraf-flowdata-test-full${FILE_SUFFIX}.out"
TEST_REPORT="${TEST_RESULTS_DIR}/flowdata_test_results${FILE_SUFFIX}.md"

# 设置MIB文件路径
MIB_PATH="${DCI_ROOT}/dci_self_test/config/mibs/minimal"
# 确保MIB目录存在
if [ ! -d "${MIB_PATH}" ]; then
    echo -e "${RED}警告: MIB目录不存在: ${MIB_PATH}${NC}"
    echo -e "${YELLOW}将使用系统默认MIB路径${NC}"
else
    echo -e "${GREEN}使用MIB目录: ${MIB_PATH}${NC}"
    # 设置环境变量
    export MIBDIRS="${MIB_PATH}"
    export MIBS="ALL"
fi

# 如果是Kafka连通性测试模式，直接执行测试并退出
if [[ "$KAFKA_ONLY_MODE" = true ]]; then
  echo -e "${GREEN}======================================${NC}"
  echo -e "${GREEN}====== Kafka连通性测试模式 =========${NC}"
  echo -e "${GREEN}======================================${NC}"
  echo

  # 检查Telegraf是否存在
  if [ ! -f "${TELEGRAF_BIN}" ]; then
    echo -e "${RED}Error: Telegraf未找到: ${TELEGRAF_BIN}${NC}"
    exit 1
  fi

  # 检查Telegraf是否可执行
  if [ ! -x "${TELEGRAF_BIN}" ]; then
    echo -e "${RED}Error: Telegraf文件不可执行，添加执行权限${NC}"
    chmod +x "${TELEGRAF_BIN}"
  fi

  # 获取Telegraf版本
  TELEGRAF_VERSION=$(${TELEGRAF_BIN} --version | head -n 1)
  echo -e "${YELLOW}Telegraf版本: ${TELEGRAF_VERSION}${NC}"
  echo -e "${YELLOW}基础配置文件: ${CONFIG_DIR}/telegraf_flowdata_complete.conf${NC}"
  echo

  # 执行Kafka连通性测试
  test_kafka_connectivity
  kafka_test_result=$?

  if [ $kafka_test_result -eq 0 ]; then
    echo -e "${GREEN}🎉 Kafka连通性测试成功完成！${NC}"
    exit 0
  else
    echo -e "${RED}💥 Kafka连通性测试失败！${NC}"
    exit 1
  fi
fi

# 确保目录存在
mkdir -p ${TEST_RESULTS_DIR}

# 清理之前的日志和输出文件
rm -f ${LOG_FILE} ${OUTPUT_FILE} ${OUTPUT_FILE_FULL}

# 打印测试信息
echo -e "${GREEN}======================================${NC}"
echo -e "${GREEN}====== Telegraf SNMP采集测试 =======${NC}"
echo -e "${GREEN}======================================${NC}"
echo

# 检查Telegraf是否存在
if [ ! -f "${TELEGRAF_BIN}" ]; then
    echo -e "${RED}Error: Telegraf未找到: ${TELEGRAF_BIN}${NC}"
    exit 1
fi

# 输出测试参数信息
echo -e "${YELLOW}采集运行时间: ${RUN_TIME}秒${NC}"

# 检查Telegraf是否可执行
if [ ! -x "${TELEGRAF_BIN}" ]; then
    echo -e "${RED}Error: Telegraf文件不可执行，添加执行权限${NC}"
    chmod +x "${TELEGRAF_BIN}"
fi

# 获取Telegraf版本
TELEGRAF_VERSION=$(${TELEGRAF_BIN} --version | head -n 1)
echo -e "${YELLOW}Telegraf版本: ${TELEGRAF_VERSION}${NC}"
echo

# 根据模式动态构建配置文件路径
if [[ "$USE_PROD_CONFIG" = true ]]; then
    TELEGRAF_CONFIG_FILE="${CONFIG_DIR}/telegraf_flowdata_prod.conf"
    SIMULATION_MODE=false # 生产配置是针对真实设备的
elif [[ "$USE_TMP_CONFIG" = true ]]; then
    TELEGRAF_CONFIG_FILE="${CONFIG_DIR}/telegraf_flowdata_tmp_kafka-test.conf"
    SIMULATION_MODE=false # tmp config is for real devices
    KAFKA_TEST_MODE=true # tmp config uses test topic
elif [[ "$USE_COMPLETE_CONFIG" = true ]]; then
    if $KAFKA_TEST_MODE; then
        TELEGRAF_CONFIG_FILE="${CONFIG_DIR}/telegraf_flowdata_complete_kafka-test.conf"
    else
        TELEGRAF_CONFIG_FILE="${CONFIG_DIR}/telegraf_flowdata_complete.conf"
    fi
    SIMULATION_MODE=false # complete config is for real devices
else
    DATA_MODE_PART="mock"
    if ! $SIMULATION_MODE; then
        DATA_MODE_PART="real"
    fi

    KAFKA_MODE_PART=""
    if $KAFKA_TEST_MODE; then
        KAFKA_MODE_PART="_kafka-test"
    fi

    TELEGRAF_CONFIG_FILE="${CONFIG_DIR}/telegraf_flowdata_${DATA_MODE_PART}${KAFKA_MODE_PART}.conf"
fi

# 根据模式选择配置文件
if [[ "$USE_PROD_CONFIG" = true ]]; then
    echo -e "${YELLOW}数据模式: 使用生产环境配置${NC}"
elif [[ "$USE_TMP_CONFIG" = true ]]; then
    echo -e "${YELLOW}数据模式: 真实SNMP状态采集 (临时配置)${NC}"
elif $SIMULATION_MODE; then
    echo -e "${YELLOW}数据模式: 模拟模式${NC}"
    GENERATOR_SCRIPT_PATH="${DCI_ROOT}/dci_self_test/scripts/snmp/generate_flow_data.sh"
    
    # 确保生成器脚本存在且可执行
    if [ ! -f "${GENERATOR_SCRIPT_PATH}" ]; then
        echo -e "${RED}错误: 模拟数据生成脚本未找到: ${GENERATOR_SCRIPT_PATH}${NC}"
        exit 1
    fi
    if [ ! -x "${GENERATOR_SCRIPT_PATH}" ]; then
        echo "为生成器脚本添加执行权限: ${GENERATOR_SCRIPT_PATH}"
        chmod +x "${GENERATOR_SCRIPT_PATH}"
    fi

    echo "模拟数据将由以下脚本按时调用生成: ${GENERATOR_SCRIPT_PATH}"
else
    # 实际SNMP采集模式
    echo -e "${YELLOW}数据模式: 真实SNMP采集${NC}"
fi

# 输出Kafka目标信息
if $KAFKA_TEST_MODE; then
    echo -e "${YELLOW}Kafka目标: 测试主题 (dci.monitor.vtest...)${NC}"
else
    echo -e "${YELLOW}Kafka目标: 真实主题 (dci.monitor.v1...)${NC}"
fi

# 检查配置文件是否存在
if [ ! -f "${TELEGRAF_CONFIG_FILE}" ]; then
    echo -e "${RED}错误: Telegraf配置文件未找到: ${TELEGRAF_CONFIG_FILE}${NC}"
    exit 1
fi
echo -e "${GREEN}使用Telegraf配置文件: ${TELEGRAF_CONFIG_FILE}${NC}"

# 为本次测试创建临时的Telegraf配置文件，以覆盖日志文件路径
TMP_TELEGRAF_CONFIG_FILE="/tmp/telegraf-test-config-$(date +%s)-${RANDOM}.conf"
# 使用 sed 的 '#' 作为分隔符，以避免与路径中的 '/' 冲突，并匹配可能存在的前导空格
sed "s#^[[:space:]]*logfile = .*#  logfile = \"${LOG_FILE}\"#" "${TELEGRAF_CONFIG_FILE}" > "${TMP_TELEGRAF_CONFIG_FILE}"
echo -e "${YELLOW}为解决日志路径问题，已动态创建临时配置文件: ${TMP_TELEGRAF_CONFIG_FILE}${NC}"
echo -e "${YELLOW}Telegraf 日志将定向到: ${LOG_FILE}${NC}"

# 提示FlowData服务状态
echo -e "${BLUE}FlowData服务信息${NC}"
echo -e "${GREEN}FlowData服务已部署到云端Kubernetes集群${NC}"
echo -e "${GREEN}流量数据将通过Kafka传输到云端FlowData服务处理${NC}"
echo

# 在测试开始前检查网络连通性
if ! $SIMULATION_MODE; then
  check_connectivity() {
    echo "检查网络连通性..."
    local all_reachable=true
    
    # 检查主机是否可达
    for host in "************"; do
      ping -c 1 -W 1 $host > /dev/null 2>&1
      if [ $? -eq 0 ]; then
        echo -e "${GREEN}√ 主机 $host 可达${NC}"
      else
        echo -e "${RED}× 主机 $host 不可达${NC}"
        all_reachable=false
        echo -e "${YELLOW}警告: 连接测试主机失败。这可能导致SNMP采集也会失败。${NC}"
      fi
    done
    
    # 尝试使用snmpget来检查SNMP连通性
    for host in "************"; do
      snmpget -v 2c -c dcilab2025 -t 1 $host .*******.*******.0 > /dev/null 2>&1
      if [ $? -eq 0 ]; then
        echo -e "${GREEN}√ 主机 $host 的SNMP服务可访问${NC}"
      else
        echo -e "${RED}× 主机 $host 的SNMP端口(161)不可访问${NC}"
        all_reachable=false
        echo -e "${YELLOW}警告: 无法连接到SNMP端口。这将导致SNMP采集失败。${NC}"
      fi
    done
    
    echo
    return 0
  }

  # 在执行主测试前调用连通性检查
  check_connectivity
fi

# 验证配置文件是否有效
echo "验证Telegraf配置..."
echo "命令：${TELEGRAF_BIN} --config ${TMP_TELEGRAF_CONFIG_FILE} --test"
${TELEGRAF_BIN} --config ${TMP_TELEGRAF_CONFIG_FILE} --test > /tmp/telegraf-test-output.log 2>&1
CONFIG_VALIDATION_EXIT_CODE=$?
if [ $CONFIG_VALIDATION_EXIT_CODE -ne 0 ]; then
  echo -e "${RED}配置验证失败！${NC}"
  echo -e "${YELLOW}Telegraf --test command output:${NC}"
  cat /tmp/telegraf-test-output.log
  echo -e "${RED}停止测试，配置验证失败${NC}"
  exit 1
else
  echo -e "${GREEN}配置验证成功！${NC}"
  CONFIG_VALID=true
fi
echo

# 启动Telegraf进行测试采集
echo "开始SNMP数据采集测试..."
echo -e "${YELLOW}将运行${RUN_TIME}秒...${NC}"

# 创建临时文件
REALTIME_OUTPUT_TMP="/tmp/telegraf-realtime-output.tmp"
rm -f ${REALTIME_OUTPUT_TMP}

# 启动Telegraf，将stdout和stderr都重定向到临时文件以便调试，并在后台运行
echo "命令：${TELEGRAF_BIN} --config ${TMP_TELEGRAF_CONFIG_FILE} > ${REALTIME_OUTPUT_TMP} 2>&1 &"
${TELEGRAF_BIN} --config ${TMP_TELEGRAF_CONFIG_FILE} > ${REALTIME_OUTPUT_TMP} 2>&1 &
TELEGRAF_PID=$!

# 使用tail -f实时显示输出，也在后台运行
echo -e "${YELLOW}实时采集数据 (包含标准输出和错误):${NC}"
echo "------------------------"
tail -f ${REALTIME_OUTPUT_TMP} &
TAIL_PID=$!

# 等待设定的运行时间
sleep $RUN_TIME

# 终止Telegraf和tail
if ps -p $TELEGRAF_PID > /dev/null; then
    kill $TELEGRAF_PID
fi
if ps -p $TAIL_PID > /dev/null; then
    kill $TAIL_PID 2>/dev/null
fi
sleep 2 # 等待数据完全写入

# 清理临时配置文件
rm -f "${TMP_TELEGRAF_CONFIG_FILE}"

echo "------------------------"
echo -e "${GREEN}数据采集完成${NC}"

# 确保输出包含数据且已保存到文件
if [ -f "${REALTIME_OUTPUT_TMP}" ]; then
    # 保存完整数据到备份文件
    cp "${REALTIME_OUTPUT_TMP}" "${OUTPUT_FILE_FULL}"
    
    # 导出格式化的JSON到临时文件
    cat "${REALTIME_OUTPUT_TMP}" | grep -v '^\\s*$' > "/tmp/telegraf-realtime-formatted.tmp"
    
    # 计算总共有多少行
    TOTAL_LINES=$(wc -l < "/tmp/telegraf-realtime-formatted.tmp")
    
    if [[ $TOTAL_LINES -gt 0 ]]; then
        # 使用head命令截取指定行数到 OUTPUT_FILE
        head -n ${OUTPUT_FILE_LINES} "/tmp/telegraf-realtime-formatted.tmp" > "${OUTPUT_FILE}"
        echo -e "${YELLOW}已限制预览输出记录数为 ${OUTPUT_FILE_LINES} 行到 ${OUTPUT_FILE}${NC}"
        echo -e "${YELLOW}完整输出已保存到 ${OUTPUT_FILE_FULL}${NC}"
    else
        cp "/tmp/telegraf-realtime-formatted.tmp" "${OUTPUT_FILE}" # 如果没有数据，OUTPUT_FILE也是空的
        echo -e "${YELLOW}未能采集到数据${NC}"
    fi
    
    # 清理临时文件
    rm -f "/tmp/telegraf-realtime-formatted.tmp"
    rm -f ${REALTIME_OUTPUT_TMP}
fi

# 验证输出文件是否存在并包含数据
echo "验证采集结果..."
if [ ! -f "${OUTPUT_FILE}" ]; then
    echo -e "${RED}输出文件不存在: ${OUTPUT_FILE}${NC}"
    TEST_SUCCESS=false
else
    # 检查输出文件大小
    FILE_SIZE=$(wc -c < "${OUTPUT_FILE}")
    if [ "${FILE_SIZE}" -eq 0 ]; then
        echo -e "${RED}输出文件为空，未采集到数据${NC}"
        TEST_SUCCESS=false
    else
        echo -e "${GREEN}成功输出采集数据到: ${OUTPUT_FILE}${NC}"
        
        # 统计记录数（只统计包含"name"字符串的行作为估算）
        TOTAL_RECORDS_IN_OUTPUT_FILE=$(grep -c "name" "${OUTPUT_FILE}")
        TOTAL_RECORDS_IN_FULL_FILE=$(grep -c "name" "${OUTPUT_FILE_FULL}")
        echo -e "${GREEN}共采集到约${TOTAL_RECORDS_IN_FULL_FILE}条记录 (预览文件包含约 ${TOTAL_RECORDS_IN_OUTPUT_FILE} 条)${NC}"
        
        TEST_SUCCESS=true
    fi
fi

# 分析数据
echo "分析采集到的数据..."
if [[ "$USE_PROD_CONFIG" = true ]]; then
    # 交换机状态指标验证
    all_status_metrics_found=true
    if grep -q "dci_switch_cpu_usage_percent" "${OUTPUT_FILE_FULL}"; then
        echo -e "${GREEN}√ 成功采集CPU使用率(dci_switch_cpu_usage_percent)${NC}"
    else
        echo -e "${RED}× 未采集到CPU使用率(dci_switch_cpu_usage_percent)${NC}"
        all_status_metrics_found=false
    fi

    if grep -q "dci_switch_memory_usage_percent" "${OUTPUT_FILE_FULL}"; then
        echo -e "${GREEN}√ 成功采集内存使用率(dci_switch_memory_usage_percent)${NC}"
    else
        echo -e "${RED}× 未采集到内存使用率(dci_switch_memory_usage_percent)${NC}"
        all_status_metrics_found=false
    fi

    if grep -q "dci_switch_interface_admin_status\\|dci_switch_interface_oper_status" "${OUTPUT_FILE_FULL}"; then
        echo -e "${GREEN}√ 成功采集接口状态(dci_switch_interface_admin_status/dci_switch_interface_oper_status)${NC}"
    else
        echo -e "${RED}× 未采集到接口状态(dci_switch_interface_admin_status/dci_switch_interface_oper_status)${NC}"
        all_status_metrics_found=false
    fi
    
    if ! $all_status_metrics_found; then
        TEST_SUCCESS=false
    fi
elif [[ "$USE_TMP_CONFIG" = true ]]; then
    # 交换机状态指标验证
    all_status_metrics_found=true
    if grep -q "hwEntityCpuUsage" "${OUTPUT_FILE_FULL}"; then
        echo -e "${GREEN}√ 成功采集CPU使用率(hwEntityCpuUsage)${NC}"
    else
        echo -e "${RED}× 未采集到CPU使用率(hwEntityCpuUsage)${NC}"
        all_status_metrics_found=false
    fi

    if grep -q "hwEntityMemUsage" "${OUTPUT_FILE_FULL}"; then
        echo -e "${GREEN}√ 成功采集内存使用率(hwEntityMemUsage)${NC}"
    else
        echo -e "${RED}× 未采集到内存使用率(hwEntityMemUsage)${NC}"
        all_status_metrics_found=false
    fi

    if grep -q "ifAdminStatus\\|ifOperStatus" "${OUTPUT_FILE_FULL}"; then
        echo -e "${GREEN}√ 成功采集接口状态(ifAdminStatus/ifOperStatus)${NC}"
    else
        echo -e "${RED}× 未采集到接口状态(ifAdminStatus/ifOperStatus)${NC}"
        all_status_metrics_found=false
    fi
    
    if ! $all_status_metrics_found; then
        TEST_SUCCESS=false
    fi
elif [[ "$USE_COMPLETE_CONFIG" = true ]]; then
    # 综合模式同时验证流量和状态指标
    all_metrics_found=true
    
    # 验证流量指标
    if grep -q "ifHCInOctets\\|ifHCOutOctets" "${OUTPUT_FILE_FULL}"; then
        echo -e "${GREEN}√ 成功采集接口流量(ifHCInOctets/ifHCOutOctets)${NC}"
    else
        echo -e "${RED}× 未采集到接口流量(ifHCInOctets/ifHCOutOctets)${NC}"
        all_metrics_found=false
    fi
    
    # 验证状态指标
    if grep -q "hwEntityCpuUsage" "${OUTPUT_FILE_FULL}"; then
        echo -e "${GREEN}√ 成功采集CPU使用率(hwEntityCpuUsage)${NC}"
    else
        echo -e "${RED}× 未采集到CPU使用率(hwEntityCpuUsage)${NC}"
        all_metrics_found=false
    fi

    if grep -q "hwEntityMemUsage" "${OUTPUT_FILE_FULL}"; then
        echo -e "${GREEN}√ 成功采集内存使用率(hwEntityMemUsage)${NC}"
    else
        echo -e "${RED}× 未采集到内存使用率(hwEntityMemUsage)${NC}"
        all_metrics_found=false
    fi

    if grep -q "ifAdminStatus\\|ifOperStatus" "${OUTPUT_FILE_FULL}"; then
        echo -e "${GREEN}√ 成功采集接口状态(ifAdminStatus/ifOperStatus)${NC}"
    else
        echo -e "${RED}× 未采集到接口状态(ifAdminStatus/ifOperStatus)${NC}"
        all_metrics_found=false
    fi
    
    if ! $all_metrics_found; then
        TEST_SUCCESS=false
    fi
else
    # 流量指标验证
    if grep -q "ifHCInOctets\\|ifHCOutOctets" "${OUTPUT_FILE_FULL}"; then
        echo -e "${GREEN}√ 成功采集接口流量(ifHCInOctets/ifHCOutOctets)${NC}"
    else
        echo -e "${RED}× 未采集到接口流量(ifHCInOctets/ifHCOutOctets)${NC}"
        TEST_SUCCESS=false
    fi

    # 检查错误和丢弃数据
    if grep -q "ifInErrors\\|ifOutErrors\\|ifInDiscards\\|ifOutDiscards" "${OUTPUT_FILE_FULL}"; then
        echo -e "${GREEN}√ 成功采集错误和丢弃计数${NC}"
    else
        echo -e "${RED}× 未采集到错误和丢弃计数${NC}"
        TEST_SUCCESS=false
    fi
fi

# 检查Kafka输出
echo "验证Kafka输出..."
if grep -q "outputs.kafka" "${LOG_FILE}"; then
    echo -e "${GREEN}√ 检测到Kafka输出${NC}"
    if grep -q "kafka_error" "${LOG_FILE}"; then
        echo -e "${RED}× Kafka输出出现错误${NC}"
        TEST_SUCCESS=false
    else
        echo -e "${GREEN}√ Kafka输出无明显错误${NC}"
        echo -e "${BLUE}数据已成功发送到Kafka，等待云端FlowData服务处理${NC}"
    fi
else
    echo -e "${RED}× 未检测到Kafka输出${NC}"
    TEST_SUCCESS=false
fi

# 生成测试报告
echo "生成测试报告..."

# Dynamically create the metrics validation part of the report
if [[ "$USE_PROD_CONFIG" = true ]]; then
  METRICS_VALIDATION_REPORT=$(cat <<EOF
| 指标类型 | 状态 |
|---------|------|
| CPU使用率 (hwEntityCpuUsage) | $(if grep -q "dci_switch_cpu_usage_percent" "${OUTPUT_FILE_FULL}"; then echo "✅"; else echo "❌"; fi) |
| 内存使用率 (hwEntityMemUsage) | $(if grep -q "dci_switch_memory_usage_percent" "${OUTPUT_FILE_FULL}"; then echo "✅"; else echo "❌"; fi) |
| 接口状态 (ifAdminStatus/ifOperStatus) | $(if grep -q "dci_switch_interface_admin_status\\|dci_switch_interface_oper_status" "${OUTPUT_FILE_FULL}"; then echo "✅"; else echo "❌"; fi) |
| 接口流量(ifHCInOctets/ifHCOutOctets) | $(if grep -q "ifHCInOctets\\|ifHCOutOctets" "${OUTPUT_FILE_FULL}"; then echo "✅"; else echo "❌"; fi) |
| Kafka输出 | $(if grep -q "outputs.kafka" "${LOG_FILE}" && ! grep -q "kafka_error" "${LOG_FILE}"; then echo "✅"; else echo "❌"; fi) |
EOF
)
elif [[ "$USE_TMP_CONFIG" = true ]]; then
  METRICS_VALIDATION_REPORT=$(cat <<EOF
| 指标类型 | 状态 |
|---------|------|
| CPU使用率 (hwEntityCpuUsage) | $(if grep -q "hwEntityCpuUsage" "${OUTPUT_FILE_FULL}"; then echo "✅"; else echo "❌"; fi) |
| 内存使用率 (hwEntityMemUsage) | $(if grep -q "hwEntityMemUsage" "${OUTPUT_FILE_FULL}"; then echo "✅"; else echo "❌"; fi) |
| 接口状态 (ifAdminStatus/ifOperStatus) | $(if grep -q "ifAdminStatus\\|ifOperStatus" "${OUTPUT_FILE_FULL}"; then echo "✅"; else echo "❌"; fi) |
| Kafka输出 | $(if grep -q "outputs.kafka" "${LOG_FILE}" && ! grep -q "kafka_error" "${LOG_FILE}"; then echo "✅"; else echo "❌"; fi) |
EOF
)
elif [[ "$USE_COMPLETE_CONFIG" = true ]]; then
  METRICS_VALIDATION_REPORT=$(cat <<EOF
| 指标类型 | 状态 |
|---------|------|
| CPU使用率 (hwEntityCpuUsage) | $(if grep -q "hwEntityCpuUsage" "${OUTPUT_FILE_FULL}"; then echo "✅"; else echo "❌"; fi) |
| 内存使用率 (hwEntityMemUsage) | $(if grep -q "hwEntityMemUsage" "${OUTPUT_FILE_FULL}"; then echo "✅"; else echo "❌"; fi) |
| 接口状态 (ifAdminStatus/ifOperStatus) | $(if grep -q "ifAdminStatus\\|ifOperStatus" "${OUTPUT_FILE_FULL}"; then echo "✅"; else echo "❌"; fi) |
| 接口流量(ifHCInOctets/ifHCOutOctets) | $(if grep -q "ifHCInOctets\\|ifHCOutOctets" "${OUTPUT_FILE_FULL}"; then echo "✅"; else echo "❌"; fi) |
| Kafka输出 | $(if grep -q "outputs.kafka" "${LOG_FILE}" && ! grep -q "kafka_error" "${LOG_FILE}"; then echo "✅"; else echo "❌"; fi) |
EOF
)
else
  METRICS_VALIDATION_REPORT=$(cat <<EOF
| 指标类型 | 状态 |
|---------|------|
| 接口流量(ifHCInOctets\\|ifHCOutOctets) | $(if grep -q "ifHCInOctets\\|ifHCOutOctets" "${OUTPUT_FILE_FULL}"; then echo "✅"; else echo "❌"; fi) |
| 错误和丢弃计数 | $(if grep -q "ifInErrors\\|ifOutErrors\\|ifInDiscards\\|ifOutDiscards" "${OUTPUT_FILE_FULL}"; then echo "✅"; else echo "❌"; fi) |
| Kafka输出 | $(if grep -q "outputs.kafka" "${LOG_FILE}" && ! grep -q "kafka_error" "${LOG_FILE}"; then echo "✅"; else echo "❌"; fi) |
EOF
)
fi

cat > ${TEST_REPORT} << EOF
# Telegraf SNMP数据采集测试报告

## 测试环境

- 测试时间: $(date)
- Telegraf版本: ${TELEGRAF_VERSION}
- 数据模式: $(if [[ "$USE_PROD_CONFIG" = true ]]; then echo "生产环境配置"; elif [[ "$USE_TMP_CONFIG" = true ]]; then echo "真实SNMP状态采集 (临时配置)"; elif [[ "$USE_COMPLETE_CONFIG" = true ]]; then echo "真实SNMP综合采集 (流量+状态)"; elif $SIMULATION_MODE; then echo "模拟数据"; else echo "真实SNMP采集"; fi)
- Kafka目标: $(if $KAFKA_TEST_MODE; then echo "测试主题"; else echo "真实主题"; fi)
- 配置文件: ${TELEGRAF_CONFIG_FILE}
- 运行时间: ${RUN_TIME}秒

## 测试结果

- 配置验证: $(if $CONFIG_VALID; then echo "成功"; else echo "失败"; fi)
- 数据采集: $(if $TEST_SUCCESS; then echo "成功"; else echo "部分成功或失败"; fi)

### 采集指标验证

${METRICS_VALIDATION_REPORT}

## 数据样例 (来自 ${OUTPUT_FILE})

\`\`\`
$(head -n 10 "${OUTPUT_FILE}") 
...
\`\`\`
完整数据请查看: ${OUTPUT_FILE_FULL}

## 结论

$(if $TEST_SUCCESS; then 
  echo "测试成功完成，所有关键指标均已正确采集。数据已成功发送至Kafka，供FlowData服务处理。"; 
else 
  echo "测试过程中发现一些问题，部分指标未能成功采集或处理。请检查详细日志以排查问题。"; 
fi)

EOF

echo -e "${GREEN}测试完成！${NC}"
echo -e "${YELLOW}测试报告已生成: ${TEST_REPORT}${NC}"

# 显示测试结果摘要
if $TEST_SUCCESS; then
  echo -e "${GREEN}测试结果: 成功${NC}"
else
  echo -e "${RED}测试结果: 部分成功或失败${NC}"
  echo -e "${YELLOW}请查看测试报告和日志文件以获取详细信息。${NC}"
fi

exit 0
